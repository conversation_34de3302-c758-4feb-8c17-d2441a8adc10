<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.casair.repository.HrBillRepository">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, client_id, pay_year, pay_monthly, bill_type, title, unit_pension_scale, unit_unemployment_scale, unit_medical_scale, work_injury_scale, personal_pension_scale,
        personal_unemployment_scale, personal_medical_scale, unit_accumulation_fund_scale, personal_accumulation_fund_scale, bill_state, review_state, is_official,is_export,
        is_salary_export,salary_payment_state,is_bonus_export,is_choice,other_bill_flag,unit_maternity_scale,bill_purpose,
        is_delete, created_by, created_date, last_modified_by, last_modified_date
    </sql>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.casair.domain.HrBill">
        <id column="id" property="id"/>
        <result column="client_id" property="clientId"/>
        <result column="pay_year" property="payYear"/>
        <result column="pay_monthly" property="payMonthly"/>
        <result column="bill_type" property="billType"/>
        <result column="title" property="title"/>
        <result column="unit_pension_scale" property="unitPensionScale"/>
        <result column="unit_unemployment_scale" property="unitUnemploymentScale"/>
        <result column="unit_medical_scale" property="unitMedicalScale"/>
        <result column="unit_maternity_scale" property="unitMaternityScale"/>
        <result column="work_injury_scale" property="workInjuryScale"/>
        <result column="personal_pension_scale" property="personalPensionScale"/>
        <result column="personal_unemployment_scale" property="personalUnemploymentScale"/>
        <result column="personal_medical_scale" property="personalMedicalScale"/>
        <result column="unit_accumulation_fund_scale" property="unitAccumulationFundScale"/>
        <result column="personal_accumulation_fund_scale" property="personalAccumulationFundScale"/>
        <result column="bill_state" property="billState"/>
        <result column="review_state" property="reviewState"/>
        <result column="is_official" property="isOfficial"/>
        <result column="is_export" property="isExport"/>
        <result column="is_salary_export" property="isSalaryExport"/>
        <result column="salary_payment_state" property="salaryPaymentState"/>
        <result column="is_delete" property="isDelete"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <sql id="Find_SQL">
        SELECT
            hb.id,
            hb.client_id,
            hc.client_name,
            hc.unit_number,
            hc.`parent_id` parentClientId,
            hb.pay_year,
            hb.pay_monthly,
            hb.bill_type,
            hb.title,
            su.real_name specialName,
            hb.bill_state,
            hb.review_state,
            hb.last_modified_date,
            hbdf.id dynamicFieldId,
            hb.other_bill_flag,
            hb.bill_purpose
        FROM hr_bill hb
        LEFT JOIN hr_client hc ON hc.id = hb.client_id AND hc.`is_delete` = 0
        LEFT JOIN sys_user su ON su.id = hc.specialized_id
        AND su.is_delete = 0
        AND su.user_status = 1
        LEFT JOIN hr_bill_dynamic_fields hbdf ON hbdf.bill_id = hb.id
        AND hbdf.is_delete = 0
        WHERE hb.is_delete = 0
        AND hb.is_official = 1
        <if test="params.ids!=null and params.ids.size()>0">
            AND hb.id IN
            <foreach collection="params.ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.clientIdList!=null and params.clientIdList.size()>0">
            AND hb.client_id IN
            <foreach collection="params.clientIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.clientIds!=null and params.clientIds.size()>0">
            AND hb.client_id IN
            <foreach collection="params.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.payYear!=null">
            AND hb.pay_year = #{params.payYear}
        </if>
        <if test="params.payMonthly!=null">
            AND hb.pay_monthly = #{params.payMonthly}
        </if>
        <if test="params.billType!=null">
            AND hb.bill_type = #{params.billType}
        </if>
        <if test="params.billTypeList!=null and params.billTypeList.size()>0">
            AND hb.bill_type IN
            <foreach collection="params.billTypeList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.title!=null and params.title!=''">
            AND hb.title LIKE CONCAT('%', #{params.title}, '%')
        </if>
        <if test="params.specialIds!=null and params.specialIds.size()>0">
            AND su.id IN
            <foreach collection="params.specialIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.billState!=null">
            AND hb.bill_state = #{params.billState}
        </if>
        <if test="params.reviewState!=null">
            AND hb.review_state = #{params.reviewState}
        </if>
        <choose>
            <when test="params.field!=null and params.field!=''">
                <choose>
                    <when test="params.field=='payment_date'">
                        ORDER BY
                        hb.pay_year ${params.order},
                        hb.pay_monthly ${params.order}
                    </when>
                    <when test="params.field=='bill_type_str'">
                        ORDER BY hb.bill_type ${params.order}
                    </when>
                    <when test="params.field=='special_name'">
                        ORDER BY specialName ${params.order}
                    </when>
                    <when test="params.field=='bill_state_str'">
                        ORDER BY hb.bill_state ${params.order}
                    </when>
                    <otherwise>
                        ORDER BY ${params.field} ${params.order}
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY
                hb.created_date DESC
            </otherwise>
        </choose>
    </sql>

    <update id="updateExportState">
        UPDATE hr_bill
            SET is_export = #{exportState}
        WHERE id IN
            <foreach collection="ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
    </update>

    <update id="updateBillReviewState">
        UPDATE
            hr_bill
        SET
            review_state = #{reviewState}
        WHERE
            id IN
            <foreach collection="billIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
    </update>

    <update id="updateBillState">
        UPDATE
        hr_bill
        SET
        bill_state = #{billState}
        WHERE
        id IN
        <foreach collection="billIdList" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

    <update id="updateSalaryExportState">
        UPDATE hr_bill
        SET is_salary_export = #{salaryExportState}
        WHERE id IN
        <foreach collection="ids" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

    <update id="updateBonusState">
        UPDATE hr_bill
        SET is_bonus_export = #{bonusExportState}
        WHERE id IN
        <foreach collection="ids" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

    <update id="updateSalaryPaymentState">
        UPDATE hr_bill
        SET salary_payment_state = #{salaryPaymentState}
        WHERE id IN
        <foreach collection="billIdList" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

    <update id="updateReviewStateAndBillState">
        UPDATE hr_bill SET review_state = #{reviewState}, bill_state = #{billState}
        WHERE id IN
        <foreach collection="billIdList" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

    <update id="updateOtherBillFlag">
        UPDATE hr_bill SET other_bill_flag = #{otherBillFlag}
        WHERE id IN
        <foreach collection="billIdList" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

    <update id="updateGrantState">
        UPDATE hr_bill SET grant_state = #{grantState} WHERE id = #{billId}
    </update>

    <select id="getBillByObject" resultType="cn.casair.dto.HrBillDTO">
        SELECT
            b.*
        FROM
            hr_bill b
        WHERE
            b.is_delete = 0
            AND b.client_id = #{params.clientId}
            AND b.pay_year = #{params.payYear}
            AND b.pay_monthly = #{params.payMonthly}
            AND b.bill_type = #{params.billType}
            AND b.is_official = #{params.isOfficial}
            AND b.bill_purpose = #{params.billPurpose}
    </select>

    <select id="getNormalSalaryEffectivePaymentDate" resultType="cn.casair.dto.HrBilNormalDTO">
        SELECT
            hb.pay_year,
            hb.pay_monthly
        FROM
            hr_bill hb
                LEFT JOIN hr_client hc ON hc.id = hb.client_id
                LEFT JOIN hr_bill_total hbt ON hbt.bill_id = hb.id
                AND hbt.is_delete = 0
        WHERE
            hb.is_delete = 0
          AND hb.bill_type = 0
          AND hb.is_official = 1
          AND hb.review_state = 1
        ORDER BY
            hb.pay_year DESC,
            hb.pay_monthly DESC
        LIMIT 1
    </select>

    <select id="findPage" resultType="cn.casair.dto.HrBillDTO">
        <include refid="Find_SQL"/>
    </select>

    <select id="findNormalSalaryPage" resultType="cn.casair.dto.HrBilNormalDTO">
        WITH b AS(
            SELECT
                any_value ( a.id ) id,
                any_value ( a.client_id ) client_id,
                any_value ( a.client_name ) client_name,
                any_value ( a.pay_year ) pay_year,
                any_value ( a.pay_monthly ) pay_monthly,
                any_value ( a.is_export ) is_export,
                SUM( a.salary ) total_income,
                (
                sum( a.personal_pension ) + sum( a.personal_unemployment ) + sum( a.personal_maternity ) + sum(
                a.personal_medical ) + sum( a.personal_accumulation_fund ) + sum( a.personal_social_security_make_up ) + sum(
                a.personal_accumulation_fund_make_up )
                ) total_welfare,
                COUNT( DISTINCT a.certificate_num ) staff_num,
                STR_TO_DATE( CONCAT( ANY_VALUE ( a.pay_year ), '-', ANY_VALUE ( a.pay_monthly ), '-', 01 ), '%Y-%m-%d' ) AS
                payDate
            FROM
                (
                SELECT
                    nsd.*,
                    IF( hns.is_export IS NULL , 0, hns.is_export ) AS is_export
                FROM
                    bill_normal_salary_detail nsd
                    LEFT JOIN hr_normal_salary hns ON hns.client_id = nsd.client_id AND hns.pay_year = nsd.pay_year AND hns.pay_monthly = nsd.pay_monthly
                WHERE
                    1 = 1
                    <if test="permissionClient!=null and permissionClient.size()>0">
                        AND nsd.client_id IN
                        <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                            #{i}
                        </foreach>
                    </if>
                    <if test="params.clientIds!=null and params.clientIds.size()>0">
                        AND nsd.client_id IN
                        <foreach collection="params.clientIds" open="(" separator="," close=")" item="i">
                            #{i}
                        </foreach>
                    </if>
                    <if test="params.clientId!=null">
                        AND nsd.client_id = #{params.clientId}
                    </if>
                    <if test="params.payYear!=null">
                        AND nsd.pay_year = #{params.payYear}
                    </if>
                    <if test="params.payMonthly!=null">
                        AND nsd.pay_monthly = #{params.payMonthly}
                    </if>
                    <if test="params.isExport != null">
                        AND nsd.is_export = #{params.isExport}
                    </if>
                ) a
            GROUP BY
                a.client_id,
                a.pay_year,
                a.pay_monthly
            ) SELECT
            id,
            client_id,
            client_name,
            pay_year,
            pay_monthly,
            total_income,
            total_welfare,
            is_export,
            staff_num,
            payDate
        FROM b
        <choose>
            <when test="params.field!=null and params.field!=''">
                <choose>
                    <when test="params.field=='payment_date'">
                        ORDER BY pay_year ${params.order}, pay_monthly ${params.order}
                    </when>
                    <when test="params.field=='total_income'">
                        ORDER BY total_income ${params.order}
                    </when>
                    <when test="params.field=='staff_num'">
                        ORDER BY staff_num ${params.order}
                    </when>
                    <when test="params.field=='total_welfare'">
                        ORDER BY total_welfare ${params.order}
                    </when>
                    <otherwise>
                        ORDER BY ${params.field} ${params.order}
                    </otherwise>
                </choose>
            </when>
        </choose>
    </select>

    <select id="getBillByPaymentDate" resultType="cn.casair.domain.HrBill">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hr_bill
        WHERE is_delete = 0 AND bill_type = 1
        AND pay_year = #{payYear}
        AND pay_monthly = #{payMonthly}
        AND is_official = 1
        <if test="clientId != null">
            AND client_id = #{clientId}
        </if>
    </select>

    <select id="getBillCountByPaymentDate" resultType="java.lang.Integer">
        SELECT count(id)
        FROM hr_bill
        WHERE is_delete = 0
          AND bill_type = #{billType}
          AND client_id = #{clientId}
          AND pay_year = #{payYear}
          AND pay_monthly = #{payMonthly}
          AND is_official = 1
    </select>

    <select id="getBillParamsByClientId" resultType="cn.casair.dto.HrBillDTO">
        SELECT hss.unit_pension          AS unitPensionScale,
               hss.work_injury           AS workInjuryScale,
               hss.unit_unemployment     AS unitUnemploymentScale,
               hss.unit_medical          AS unitMedicalScale,
               hss.personal_pension      AS personalPensionScale,
               hss.personal_unemployment AS personalUnemploymentScale,
               hss.personal_medical      AS personalMedicalScale,
               hss.unit_maternity        AS unitMaternityScale,
               hss.personal_maternity    AS personalMaternityScale,
               haf.unit_scale            AS unitAccumulationFundScale,
               haf.personage_scale       AS personalAccumulationFundScale,
               hc.client_name
        FROM hr_client hc
                 LEFT JOIN hr_social_security hss ON hc.social_security_type_id = hss.id
                 LEFT JOIN hr_accumulation_fund haf ON hc.provident_fund_type_id = haf.id
        WHERE hc.id = #{clientId}
    </select>

    <sql id="Bill_Info">
        SELECT b.*,
               hc.unit_number,
               hc.client_name,
               hc2.client_name AS parentClientName,
               hbdf.origin_bill_name,
               hbdf.origin_bill_url
        FROM hr_bill b
                 LEFT JOIN hr_client hc ON b.client_id = hc.id
                 LEFT JOIN hr_client hc2 ON hc.parent_id = hc2.id
                 LEFT JOIN hr_bill_dynamic_fields hbdf ON hbdf.bill_id = b.id
                AND hbdf.is_delete = 0
    </sql>

    <select id="getBillInfoById" resultType="cn.casair.dto.HrBillDTO">
        <include refid="Bill_Info"/>
        WHERE b.id = #{id} OR b.bill_no = #{id}
        LIMIT 1
    </select>

    <select id="findList" resultType="cn.casair.dto.HrBillDTO">
        <include refid="Find_SQL"/>
    </select>

    <select id="getSecurityBill" resultType="java.lang.String">
        SELECT DISTINCT client_id FROM  hr_bill
        WHERE is_delete = 0
        AND is_official = 1
        AND bill_type = #{billType}
        AND pay_year = #{payYear}
        AND pay_monthly = #{payMonthly}
        <if test="billType == 0">
            AND bill_state = 1
        </if>
        <if test="clientIds != null and clientIds.size() > 0">
            AND client_id IN
            <foreach collection="clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
    </select>

    <sql id="Select_SQL">
        WITH a AS (
            SELECT
                any_value (hb.id) id,
                any_value (hb.client_id) client_id,
                any_value (hc.client_name) client_name,
                any_value (hb.pay_year) pay_year,
                any_value (hb.pay_monthly) pay_monthly,
                any_value (hb.bill_state) bill_state,
                any_value (hb.is_salary_export) is_salary_export,
                any_value (hb.grant_state) grant_state,
                GROUP_CONCAT(DISTINCT hbd.staff_id) staffIds,
                GROUP_CONCAT(hbd.id) billDetailIds,
                GROUP_CONCAT(DISTINCT hb.id) billIds,
                GROUP_CONCAT(DISTINCT hb.is_salary_export) is_salary_exports,
                COUNT(DISTINCT hbd.certificate_num) staff_num,
                SUM(hbd.real_salary) real_salary
        FROM
                hr_bill hb
            LEFT JOIN hr_client hc ON hc.id = hb.client_id
            LEFT JOIN hr_bill_total hbt ON hbt.bill_id = hb.id
            LEFT JOIN hr_bill_detail hbd ON hbd.bill_id = hb.id
            WHERE hb.is_delete = 0 AND hb.bill_type = 0 AND hb.is_official = 1
            AND hb.review_state = 1 AND hb.salary_payment_state = 1  AND hbt.is_delete = 0
            AND hbd.is_delete = 0 AND hbd.is_used = 1
            <if test="permissionClient != null and permissionClient.size() > 0">
                AND hc.id IN
                <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
            GROUP BY  hb.id
        ) SELECT
            id,
            client_id,
            client_name,
            pay_year,
            pay_monthly,
            bill_state,
            staffIds,
            billDetailIds,
            staff_num,
            billIds,
            is_salary_export,
            is_salary_exports,
            real_salary,
            grant_state
        FROM a
        <where>
            <if test="params.clientIds!=null and params.clientIds.size()>0">
                AND client_id IN
                <foreach collection="params.clientIds" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
            <if test="params.ids!=null and params.ids.size()>0">
                AND id IN
                <foreach collection="params.ids" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
            <if test="params.grantStateList!=null and params.grantStateList.size()>0">
                AND grant_state IN
                <foreach collection="params.grantStateList" open="(" separator="," close=")" item="i">
                    #{i}
                </foreach>
            </if>
            <if test="params.payYear!=null">
                AND pay_year = #{params.payYear}
            </if>
            <if test="params.payMonthly!=null">
                AND pay_monthly = #{params.payMonthly}
            </if>
            <if test="params.isSalaryExport != null and params.isSalaryExport == 0">
                AND is_salary_exports LIKE CONCAT('%', 0, '%')
            </if>
            <if test="params.isSalaryExport != null and params.isSalaryExport == 1">
                AND is_salary_exports NOT LIKE CONCAT('%', 0, '%')
            </if>
            <if test="params.billState!=null">
                AND bill_state = #{params.billState}
            </if>
            <if test="params.grantState!=null">
                AND grant_state = #{params.grantState}
            </if>
        </where>
        <choose>
            <when test="params.field!=null and params.field!=''">
                <choose>
                    <when test="params.field=='payment_date'">
                        ORDER BY pay_year ${params.order}, pay_monthly ${params.order}
                    </when>
                    <otherwise>
                        ORDER BY ${params.field} ${params.order}
                    </otherwise>
                </choose>
            </when>
        </choose>
    </sql>

    <select id="findSalaryPaymentPage" resultType="cn.casair.dto.HrSalaryPaymentDTO">
        <include refid="Select_SQL"/>
    </select>

    <select id="findSalaryPaymentList" resultType="cn.casair.dto.HrSalaryPaymentDTO">
        <include refid="Select_SQL"/>
    </select>

    <sql id="Query_SQL">
        SELECT
        any_value ( hb.id ) id,
        any_value ( hb.client_id ) client_id,
        any_value ( hc.client_name ) client_name,
        any_value ( hb.pay_year ) pay_year,
        any_value ( hb.pay_monthly ) pay_monthly,
        any_value ( hb.is_bonus_export ) is_bonus_export,
        COUNT(hbt.staff_num) staff_num,
        sum(hbd.total) bonus_total
        FROM
        hr_bill hb
        LEFT JOIN hr_client hc ON hc.id = hb.client_id
        LEFT JOIN hr_bill_total hbt ON hbt.bill_id = hb.id
        LEFT JOIN hr_bill_detail hbd ON hbd.bill_id = hb.id
        WHERE
        hb.is_delete = 0
        AND hb.bill_type = 0
        AND hb.is_official = 1
        AND hbt.is_delete = 0
        AND hbd.is_delete = 0
        AND hbd.tax_calculation_method = 1
        <if test="permissionClient!=null and permissionClient.size()>0">
            AND hc.id IN
            <foreach collection="permissionClient" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.clientIds!=null and params.clientIds.size()>0">
            AND hb.client_id IN
            <foreach collection="params.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.ids!=null and params.ids.size()>0">
            AND hb.id IN
            <foreach collection="params.ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.payYear!=null">
            AND hb.pay_year = #{params.payYear}
        </if>
        <if test="params.payMonthly!=null">
            AND hb.pay_monthly = #{params.payMonthly}
        </if>
        <if test="params.isBonusExport!=null">
            AND hb.is_bonus_export = #{params.isBonusExport}
        </if>
        GROUP BY hb.client_id, hb.pay_year, hb.pay_monthly
        <choose>
            <when test="params.field!=null and params.field!=''">
                <choose>
                    <when test="params.field=='payment_date'">
                        ORDER BY pay_year ${params.order}, pay_monthly ${params.order}
                    </when>
                    <when test="params.field=='total_welfare'">
                        ORDER BY bonus_total ${params.order}
                    </when>
                    <otherwise>
                        ORDER BY ${params.field} ${params.order}
                    </otherwise>
                </choose>
            </when>
        </choose>
    </sql>
    <select id="findAnnualLumpSumBonusPage" resultType="cn.casair.dto.HrAnnualBonusDTO">
       <include refid="Query_SQL"/>
    </select>

    <select id="findAnnualLumpSumBonusList" resultType="cn.casair.dto.HrAnnualBonusDTO">
        <include refid="Query_SQL"/>
    </select>

    <select id="getBillInfoByIdList" resultType="cn.casair.dto.HrBillDTO">
        SELECT
            b.*, hc.unit_number,
            hc.client_name AS parentClientName,
            hbdf.origin_bill_name,
            hbdf.origin_bill_url
        FROM
            hr_bill b
        LEFT JOIN hr_client hc ON b.client_id = hc.id
        LEFT JOIN hr_bill_dynamic_fields hbdf ON hbdf.bill_id = b.id AND hbdf.is_delete = 0
        WHERE b.id IN
        <foreach collection="billIdList" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
        <if test="isChoice != null">
            AND b.is_choice = #{isChoice}
        </if>
        LIMIT 1
    </select>

    <select id="findBillList" resultType="cn.casair.dto.HrBillDTO">
        SELECT
            hb.id,
            hc.client_name,
            hb.title,
            hb.bill_type,
            hbt.total
        FROM
            hr_bill hb
        LEFT JOIN hr_client hc ON hb.client_id = hc.id
        LEFT JOIN hr_bill_total hbt ON hbt.bill_id = hb.id AND hbt.is_delete = 0
        WHERE hb.is_delete = 0 AND hb.is_official = 1
        <if test="params.clientIdList!=null and params.clientIdList.size()>0">
            AND hb.client_id IN
            <foreach collection="params.clientIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.ids!=null and params.ids.size()>0">
            AND hb.id IN
            <foreach collection="params.ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.billType!=null">
            AND hb.bill_type = #{params.billType}
        </if>
        <if test="params.payYear!=null">
            AND hb.pay_year = #{params.payYear}
        </if>
        <if test="params.payMonthly!=null">
            AND hb.pay_monthly = #{params.payMonthly}
        </if>
        ORDER BY hb.created_date DESC
    </select>

    <select id="queryBillInfo" resultType="cn.casair.dto.HrBillDTO">
        SELECT * FROM (
            SELECT
                hb.id,
                hb.client_id,
                hc.client_name,
                hb.title,
                hb.bill_type,
                hbt.total,
                CONCAT( hb.pay_year,'-',IF(hb.pay_monthly>9,hb.pay_monthly,CONCAT('0',hb.pay_monthly))) AS payment_date,
                hb.created_date,
                hb.is_delete
            FROM
                hr_bill hb
            LEFT JOIN hr_client hc ON hb.client_id = hc.id
            LEFT JOIN hr_bill_total hbt ON hbt.bill_id = hb.id AND hbt.is_delete = 0 AND hbt.sinopec_type = 1
            WHERE hb.is_delete = 0 AND hb.is_official = 1 AND hb.bill_state = 0
            AND (hb.review_state IS NULL OR hb.review_state IN (2,4))
        ) hb
        WHERE hb.is_delete = 0
        <if test="params.clientIdList!=null and params.clientIdList.size()>0">
            AND hb.client_id IN
            <foreach collection="params.clientIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.feeReviewDate != null">
            AND hb.payment_date = #{params.feeReviewDate}
        </if>
        <if test="params.paymentDateStart != null">
            AND hb.payment_date >= #{params.paymentDateStart}
        </if>
        <if test="params.paymentDateEnd != null">
            AND hb.payment_date <![CDATA[ <= ]]> #{params.paymentDateEnd}
        </if>
        ORDER BY hb.created_date DESC
    </select>

    <select id="getBillInfoBatch" resultType="cn.casair.dto.HrBillDTO">
        <include refid="Bill_Info"/>
        WHERE b.id IN
        <foreach collection="ids" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </select>

    <select id="findUnissuedSalaryList" resultType="cn.casair.dto.HrBilNormalDTO">
        SELECT
            hb.id,
            ANY_VALUE(hb.grant_state) grant_state,
            SUM(IF(
                hbd.salary = 0 OR hbd.salary IS NULL,
                ( hbd.personal_pension + hbd.personal_unemployment + hbd.personal_medical + hbd.personal_accumulation_fund ),
                hbd.salary + hbd.personal_social_security_make_up + hbd.personal_accumulation_fund_make_up
            )) total_income
        FROM
            hr_bill hb
            LEFT JOIN hr_bill_detail hbd ON hb.id = hbd.bill_id
        WHERE hb.is_delete = 0 AND hb.bill_type = 0 AND hb.is_official = 1 AND hb.review_state = 1
            AND hbd.is_delete = 0 AND hbd.is_used = 1 AND hb.grant_state = 3
        GROUP BY hb.id
    </select>

    <select id="findRepeatBill" resultType="java.lang.String">
        SELECT GROUP_CONCAT(id) ids FROM hr_bill WHERE is_delete = 0 AND is_official = 1 AND bill_type = 0 GROUP BY client_id,pay_year,pay_monthly HAVING COUNT(1) > 1
    </select>

    <delete id="deleteBatchBill">
        DELETE FROM hr_bill WHERE pay_year = #{param.payYear} AND pay_monthly = #{param.payMonthly} AND title = #{param.title}
    </delete>

    <delete id="deleteByBillIds">
        DELETE FROM hr_bill WHERE id IN
        <foreach collection="billIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </delete>

    <update id="delByCompareResult">
        UPDATE hr_bill SET is_delete = 1, last_modified_by = #{lastModifiedBy}, last_modified_date = now() WHERE title LIKE CONCAT('%', #{title}, '%')
        AND id IN
        <foreach collection="billIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </update>

    <update id="updateSinopecContractNo">
         UPDATE hr_bill SET contract_no_show = #{contractNoFlag}, last_modified_date = now() WHERE id = #{billId}
    </update>

    <select id="findRecordPage" resultType="cn.casair.dto.HrBillDTO">
        SELECT
        hb.bill_no id,
        hb.bill_no,
        hb.`opt_client_id`,
        any_value(hb.`opt_title`) title,
        min(hb.bill_state) bill_state,
        min(hb.review_state) review_state,
        GROUP_CONCAT(hb.`client_id`) strClientIds,
        GROUP_CONCAT(hb.`id`) strBillIds,
        any_value(hc.`client_name`) client_name,
        hb.pay_year,
        hb.pay_monthly,
        hb.bill_type,
        any_value(hb.last_modified_date) last_modified_date,
        any_value(su.real_name) specialName,
        any_value(hbdf.id) dynamicFieldId,
        any_value(hb.other_bill_flag) other_bill_flag,
        any_value(hb.bill_purpose) bill_purpose
        FROM hr_bill hb
        LEFT JOIN hr_client hc ON hc.id = hb.opt_client_id AND hc.`is_delete` = 0
        LEFT JOIN sys_user su ON su.id = hc.specialized_id AND su.is_delete = 0 AND su.user_status = 1
        LEFT JOIN hr_bill_dynamic_fields hbdf ON hbdf.bill_id = hb.id AND hbdf.is_delete = 0
        WHERE hb.is_delete = 0
        AND hb.is_official = 1
        <if test="params.ids!=null and params.ids.size()>0">
            AND hb.id IN
            <foreach collection="params.ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.clientIdList!=null and params.clientIdList.size()>0">
            AND hb.client_id IN
            <foreach collection="params.clientIdList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.clientIds!=null and params.clientIds.size()>0">
            AND hb.client_id IN
            <foreach collection="params.clientIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.payYear!=null">
            AND hb.pay_year = #{params.payYear}
        </if>
        <if test="params.payMonthly!=null">
            AND hb.pay_monthly = #{params.payMonthly}
        </if>
        <if test="params.billType!=null">
            AND hb.bill_type = #{params.billType}
        </if>
        <if test="params.billTypeList!=null and params.billTypeList.size()>0">
            AND hb.bill_type IN
            <foreach collection="params.billTypeList" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.title!=null and params.title!=''">
            AND hb.title LIKE CONCAT('%', #{params.title}, '%')
        </if>
        <if test="params.specialIds!=null and params.specialIds.size()>0">
            AND su.id IN
            <foreach collection="params.specialIds" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="params.billState!=null">
            AND hb.bill_state = #{params.billState}
        </if>
        <if test="params.reviewState!=null">
            AND hb.review_state = #{params.reviewState}
        </if>
        GROUP BY hb.bill_no, hb.`opt_client_id`, hb.pay_year, hb.pay_monthly, hb.bill_type
        <choose>
            <when test="params.field!=null and params.field!=''">
                <choose>
                    <when test="params.field=='payment_date'">
                        ORDER BY
                        hb.pay_year ${params.order},
                        hb.pay_monthly ${params.order}
                    </when>
                    <when test="params.field=='bill_type_str'">
                        ORDER BY hb.bill_type ${params.order}
                    </when>
                    <when test="params.field=='special_name'">
                        ORDER BY specialName ${params.order}
                    </when>
                    <when test="params.field=='bill_state_str'">
                        ORDER BY hb.bill_state ${params.order}
                    </when>
                    <otherwise>
                        ORDER BY ${params.field} ${params.order}
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY
                hb.created_date DESC
            </otherwise>
        </choose>
    </select>

    <select id="findByBillNo" resultType="cn.casair.dto.HrBillDTO">
        WITH RECURSIVE t1 AS (
            SELECT c1.id, c1.client_name, c1.parent_id,c1.specialized_id
            FROM hr_client c1
            WHERE id = #{clientId}
            UNION ALL
            SELECT c2.id, c2.client_name, c2.parent_id,c2.specialized_id
            FROM hr_client c2, t1
            WHERE c2.parent_id = t1.id
        )
        SELECT
            ifnull(hb.id,t1.id) id ,
            hb.bill_no,
            hb.`opt_client_id`,
            t1.id clientId,
            t1.client_name,
            t1.parent_id parentClientId,
            hb.pay_year,
            hb.pay_monthly,
            hb.bill_type,
            hb.title,
            su.real_name specialName,
            IFNULL(hb.bill_state,1) bill_state,
            hb.review_state,
            hb.created_date,
            hb.last_modified_date,
            hbdf.id dynamicFieldId,
            hb.other_bill_flag,
            hb.bill_purpose
        FROM t1 LEFT JOIN
             hr_bill hb ON hb.client_id = t1.id AND hb.bill_no = #{billNo}
                LEFT JOIN sys_user su ON su.id = t1.specialized_id
            AND su.is_delete = 0
            AND su.user_status = 1
                LEFT JOIN hr_bill_dynamic_fields hbdf ON hbdf.bill_id = hb.id
            AND hbdf.is_delete = 0

    </select>

    <select id="findSocialConfig" resultType="cn.casair.dto.HrBillDTO">
        SELECT
            *
        FROM
            hr_bill
        WHERE is_delete = 0 AND ( special_field IS NOT NULL  OR alone_cardinal IS NOT NULL  OR merge_cardinal IS NOT NULL )
        AND id IN
        <foreach collection="billIds" open="(" separator="," close=")" item="i">
            #{i}
        </foreach>
    </select>

    <select id="selectByIdOrBillNo" resultType="cn.casair.domain.HrBill">
        SELECT
            *
        FROM
            hr_bill
        WHERE
            is_delete = 0
            AND (id = #{value} OR bill_no = #{value})
        ORDER BY created_date DESC
    </select>

</mapper>
