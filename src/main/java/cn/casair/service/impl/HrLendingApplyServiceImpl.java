package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.common.enums.LendingApplyEnum;
import cn.casair.common.enums.ServiceCenterEnum;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.RandomUtil;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.mapper.HrArchivesBringMapper;
import cn.casair.mapper.HrLendingApplyMapper;
import cn.casair.repository.*;
import cn.casair.service.*;
import cn.casair.service.util.SecurityUtils;
import cn.casair.web.rest.util.ResponseUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 借阅申请表服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-30
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class HrLendingApplyServiceImpl extends ServiceImpl<HrLendingApplyRepository, HrLendingApply> implements HrLendingApplyService {


    private final HrLendingApplyRepository hrLendingApplyRepository;
    private final HrLendingApplyMapper hrLendingApplyMapper;
    private final HrAppendixService hrAppendixService;
    private final SysOperLogService sysOperLogService;
    private final HrTalentStaffService hrTalentStaffService;

    public HrLendingApplyServiceImpl(HrLendingApplyRepository hrLendingApplyRepository,
                                     HrLendingApplyMapper hrLendingApplyMapper,
                                     HrAppendixService hrAppendixService,
                                     SysOperLogService sysOperLogService,
                                     HrTalentStaffService hrTalentStaffService) {
        this.hrLendingApplyRepository = hrLendingApplyRepository;
        this.hrLendingApplyMapper = hrLendingApplyMapper;
        this.hrAppendixService = hrAppendixService;
        this.sysOperLogService = sysOperLogService;
        this.hrTalentStaffService = hrTalentStaffService;
    }

    @Resource
    private HrApplyOpLogsService hrApplyOpLogsService;
    @Resource
    private HrLendingApplyTpyeRelationService hrLendingApplyTpyeRelationService;
    @Resource
    private HrClientService hrClientService;
    @Resource
    private HrArchivesDetailService hrArchivesDetailService;
    @Resource
    private HrApplyOpLogsRepository hrApplyOpLogsRepository;
    @Resource
    private HrArchivesBringService hrArchivesBringService;
    @Resource
    private HrArchivesManageRepository hrArchivesManageRepository;
    @Resource
    private HrArchivesBringRepository hrArchivesBringRepository;
    @Resource
    private HrArchivesBringMapper hrArchivesBringMapper;
    @Resource
    private HrArchivesDetailRepository hrArchivesDetailRepository;
    @Resource
    private HrLendingApplyTpyeRelationRepository hrLendingApplyTpyeRelationRepository;
    @Resource
    private UserService userService;
    @Resource
    private HrAppletMessageService hrAppletMessageService;
    @Resource
    private HrUpcomingService hrUpcomingService;
    @Resource
    private HrNotificationUserService hrNotificationUserService;

    /**
     * 创建借阅申请表
     *
     * @param hrLendingApplyDTO
     * @param requestFromWx 是否是微信小程序的请求
     * @return
     */
    @Override
    public HrLendingApplyDTO createHrLendingApply(HrLendingApplyDTO hrLendingApplyDTO, Boolean requestFromWx) {
        log.info("Create new HrLendingApply:{}", hrLendingApplyDTO);
        HrLendingApply hrLendingApply = this.hrLendingApplyMapper.toEntity(hrLendingApplyDTO);
        if (hrLendingApplyDTO.getEndDate() != null) {
//            if (!hrLendingApplyDTO.getEndDate().isAfter( LocalDate.now() )) {
//                throw new CommonException( "请选择一个晚于今天的日期！" );
//            }
            long l = hrLendingApplyDTO.getEndDate().toEpochDay() - LocalDate.now().toEpochDay();
            log.info("**date:*" + hrLendingApplyDTO.getEndDate() + "*****l:" + l + "*********");
            if (l >= 90) {
                throw new CommonException("借阅期最长不超过90天！");
            }
        }
        // 获取员工id
        String staffId = hrLendingApplyDTO.getStaffId();
        if (requestFromWx) {
            JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
            staffId = jwtMiniDTO.getId();
        }
        if (StringUtils.isBlank(staffId)) {
            throw new CommonException("员工id不能为空");
        }
        // 获取员工信息
        HrTalentStaff hrTalentStaff = this.hrTalentStaffService.getById(staffId);
        // 插入档案借阅
        hrLendingApply.setId(RandomUtil.generateId());
        hrLendingApply.setStartDate(LocalDate.now());
        hrLendingApply.setClientId(hrTalentStaff.getClientId());
        hrLendingApply.setStaffId(hrTalentStaff.getId());
        List<String> typeIds = hrLendingApplyDTO.getTypeIds();
        if (typeIds != null && !typeIds.isEmpty()) {
            List<HrArchivesDetail> hrArchivesDetails = hrArchivesDetailService.listByIds(typeIds);
            hrArchivesDetails.forEach(detail ->{
                HrLendingApplyTpyeRelationDTO hrLendingApplyTpyeRelation = new HrLendingApplyTpyeRelationDTO(hrLendingApply.getId(), detail.getId());
                hrLendingApplyTpyeRelationService.createHrLendingApplyTpyeRelation(hrLendingApplyTpyeRelation);
            });
        }
        this.hrLendingApplyRepository.insert(hrLendingApply);
        HrLendingApplyDTO lendingApplyDTO = this.hrLendingApplyMapper.toDto(hrLendingApply);
        //创建操作日志
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("lendingStatus", ServiceCenterEnum.LENDING_APPLY.getKey());
        jsonObject.put("message", "借阅申请已提交");
        String message = hrTalentStaff.getName() + "发起了档案借阅申请。####" + jsonObject.toJSONString();
        if (requestFromWx) {
            this.hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(hrLendingApply.getId(), null, hrTalentStaff.getId(), message, null, true, null, ServiceCenterEnum.LENDING_APPLY.getKey());
        } else {
            JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
            message = jwtUserDTO.getRealName() + "从管理端为" + message;
            this.hrApplyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrLendingApply.getId(), null, hrTalentStaff.getId(), message, null, ServiceCenterEnum.LENDING_APPLY.getKey());
        }
        // 发送微信通知
        hrAppletMessageService.saveNoticeMessage(ServiceCenterEnum.LENDING_APPLY.getKey(), hrLendingApply.getId(), hrTalentStaff.getId(), String.valueOf(jsonObject.get("message")), false, null);
        // 保存提醒内容
        this.hrNotificationUserService.saveRemindContent(lendingApplyDTO.getClientId(), ServiceCenterEnum.LENDING_APPLY.getKey(), ServiceCenterEnum.LendingApplyEnum.INITIATE_APPLICATION.getKey(), hrTalentStaff.getName() + "发起了档案借阅申请。等待专管员审核", hrTalentStaff.getId());
        // 生成待办
        hrUpcomingService.createServiceUpcoming(lendingApplyDTO.getId(), lendingApplyDTO.getStaffId(), "档案借阅-审核" + hrTalentStaff.getName() + "发起的档案借阅申请", LocalDate.now(), 0);
        // 系统日志（缺失）
        /*HrLendingApply logApply = this.hrLendingApplyRepository.selectById(hrLendingApply.getId());
        HrLendingApplyDTO logDto = this.hrLendingApplyMapper.toDto(logApply);
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.RETIRE.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(logDto),
            HrLendingApplyDTO.class,
            null,
            JSON.toJSONString(logDto)
        );*/
        return lendingApplyDTO;
    }

    /**
     * 修改借阅申请表
     *
     * @param hrLendingApplyDTO
     * @return
     */
    @Override
    public Optional<HrLendingApplyDTO> updateHrLendingApply(HrLendingApplyDTO hrLendingApplyDTO) {
        return Optional.ofNullable(this.hrLendingApplyRepository.selectById(hrLendingApplyDTO.getId()))
            .map(roleTemp -> {
                HrLendingApply hrLendingApply = this.hrLendingApplyMapper.toEntity(hrLendingApplyDTO);
                this.hrLendingApplyRepository.updateById(hrLendingApply);
                log.info("Update HrLendingApply:{}", hrLendingApplyDTO);
                return hrLendingApplyDTO;
            });
    }

    /**
     * 查询借阅申请表详情
     *
     * @param id
     * @return
     */
    @Override
    public HrLendingApplyDTO getHrLendingApply(String id) {
        log.info("Get HrLendingApply :{}", id);
        HrLendingApplyDTO hrLendingApplyDTO = this.hrLendingApplyRepository.getLendingApplyById(id);
        QueryWrapper<HrLendingApplyTpyeRelation> queryWrapper = new QueryWrapper();
        queryWrapper.eq("lending_apply_id", hrLendingApplyDTO.getId());
        queryWrapper.eq("is_delete", 0);
        List<HrLendingApplyTpyeRelation> tpyeRelationList = hrLendingApplyTpyeRelationRepository.selectList(queryWrapper);
        List<HrArchivesDetailDTO> detailDTOList = new ArrayList<>();
        String typeListStr = hrLendingApplyDTO.getTypeListStr();
        if (typeListStr.contains("线下提档")){
            detailDTOList.add(new HrArchivesDetailDTO().setName("线下提档").setType(1));
        }
        if (tpyeRelationList != null && !tpyeRelationList.isEmpty()){
            String idsStr = tpyeRelationList.stream().map(HrLendingApplyTpyeRelation::getTypeId).collect(Collectors.joining(","));
            List<HrArchivesDetailDTO> list = hrArchivesDetailRepository.getDetailByDetailIds(idsStr);
            if (list.size() > 0) {
                detailDTOList.addAll(list);
            }
        } else {
            //线下提档默认档案类型为员工档案
            hrLendingApplyDTO.setArchivesType(0).setArchivesName(hrLendingApplyDTO.getStaffName());
        }
        hrLendingApplyDTO.setArchivesDetailList(detailDTOList);
        //返回申请操作日志
        List<HrApplyOpLogsDTO> opLogsList = hrApplyOpLogsService.findApplyOpLogsList(id, null);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(opLogsList)) {
            hrLendingApplyDTO.setOpLogsList(opLogsList);
            //TODO 取 message中的lendingStatus 放入集合取最大值，判断流程是否大于3或者9，如果大于3判断集合中是否有3,9判断一样
            List<Integer> statusList = new ArrayList<>();
            for (HrApplyOpLogsDTO hrApplyOpLogsDTO : opLogsList) {
                if (StringUtils.isNotBlank(hrApplyOpLogsDTO.getMessage())) {
                    String[] split = hrApplyOpLogsDTO.getMessage().split("####");
                    String lastOne = split[split.length - 1];
                    Integer lendingStatus = new JSONObject().parseObject(lastOne).getInteger("lendingStatus");
                    statusList.add(lendingStatus);
                }
            }
            List distinctList = (List) statusList.stream().distinct().collect(Collectors.toList());
            Optional max = distinctList.stream().max(Comparator.comparing(Integer::intValue));
            Integer maxValue = (Integer) max.get();
            if (maxValue >= 3 && maxValue < 9) {
                if (distinctList.contains(3)) {
                    hrLendingApplyDTO.setIsCustomerReviewed(true);
                    hrLendingApplyDTO.setIsPickUp(false);
                } else {
                    hrLendingApplyDTO.setIsCustomerReviewed(false);
                    hrLendingApplyDTO.setIsPickUp(false);
                }
            } else if (maxValue >= 9) {
                if (distinctList.contains(3)) {
                    hrLendingApplyDTO.setIsCustomerReviewed(true);
                } else {
                    hrLendingApplyDTO.setIsCustomerReviewed(false);
                }
                if (distinctList.contains(9)) {
                    hrLendingApplyDTO.setIsPickUp(true);
                } else {
                    hrLendingApplyDTO.setIsPickUp(false);
                }
            } else {
                hrLendingApplyDTO.setIsCustomerReviewed(false);
                hrLendingApplyDTO.setIsPickUp(false);
            }
        } else {
            hrLendingApplyDTO.setIsCustomerReviewed(false);
            hrLendingApplyDTO.setIsPickUp(false);
        }
        return hrLendingApplyDTO;
    }

    /**
     * 删除借阅申请表
     *
     * @param id
     */
    @Override
    public void deleteHrLendingApply(String id) {
        Optional.ofNullable(this.hrLendingApplyRepository.selectById(id))
            .ifPresent(hrLendingApply -> {
                this.hrLendingApplyRepository.deleteById(id);
                log.info("Delete HrLendingApply:{}", hrLendingApply);
            });
    }

    /**
     * 批量删除借阅申请表
     *
     * @param ids
     */
    @Override
    public void deleteHrLendingApply(List<String> ids) {
        log.info("Delete HrLendingApplys:{}", ids);
        this.hrLendingApplyRepository.deleteBatchIds(ids);
    }

    /**
     * 分页查询借阅申请表
     *
     * @param hrLendingApplyDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrLendingApplyDTO hrLendingApplyDTO, Long pageNumber, Long pageSize) {
        fillQueryDTO(hrLendingApplyDTO);
        Page<HrLendingApply> page = new Page<>(pageNumber, pageSize);
        IPage<HrLendingApplyDTO> iPage = this.hrLendingApplyRepository.selectLendingApplyPage(page, hrLendingApplyDTO);
        List<HrLendingApplyDTO> records = iPage.getRecords();
        fillApplyStatus(records);
        return iPage;
    }

    private void fillQueryDTO(HrLendingApplyDTO hrLendingApplyDTO) {
        //查询数据权限
        List<String> clientIds = hrClientService.selectClientIdByUserId();
        if (CollectionUtils.isEmpty(clientIds)) {
            clientIds.add("");
        }
        hrLendingApplyDTO.setClientIds(clientIds);
        if (CollectionUtils.isNotEmpty(hrLendingApplyDTO.getApplyDateList())) {
            LocalDate startDate = LocalDate.parse(hrLendingApplyDTO.getApplyDateList().get(0), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            LocalDate endDate = LocalDate.parse(hrLendingApplyDTO.getApplyDateList().get(1), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            hrLendingApplyDTO.setCreatedStartDate(startDate);
            hrLendingApplyDTO.setCreatedEndDate(endDate);
        }
        List<Integer> findStateList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(hrLendingApplyDTO.getStateList())) {
            List<Integer> stateList = hrLendingApplyDTO.getStateList();
            for (Integer state : stateList) {
                if (state == 0) {//专管员审核
                    findStateList.add(LendingApplyEnum.LendingApplyType.TO_BE_MANAGER_REVIEWED.getKey());
                } else if (state == 1) {//客户审核
                    findStateList.add(LendingApplyEnum.LendingApplyType.TO_BE_CUSTOMER_REVIEWED.getKey());
                } else if (state == 2) {//客服经理审核--已去除客服经理审核
                    findStateList.add(LendingApplyEnum.LendingApplyType.TO_BE_CUSTOMER_MANAGER_REVIEWED.getKey());
                } else if (state == 3) {//档案管理员审核
                    findStateList.add(LendingApplyEnum.LendingApplyType.TO_BE_FILE_MANAGER_REVIEWED.getKey());
                } else if (state == 4) {//待提取
                    findStateList.add(LendingApplyEnum.LendingApplyType.TO_BE_PICK_UP.getKey());
                } else if (state == 5) {//已结束或审核拒绝
                    findStateList.add(LendingApplyEnum.LendingApplyType.MANAGER_REJECTED.getKey());
                    findStateList.add(LendingApplyEnum.LendingApplyType.CUSTOMER_REJECTED.getKey());
                    findStateList.add(LendingApplyEnum.LendingApplyType.CUSTOMER_MANAGER_REJECTED.getKey());
                    findStateList.add(LendingApplyEnum.LendingApplyType.FILE_MANAGER_REJECTED.getKey());
                    findStateList.add(LendingApplyEnum.LendingApplyType.TO_BE_FINISH.getKey());
                }
            }
            if (findStateList.size() > 0) {
                hrLendingApplyDTO.setFindStateList(findStateList);
            }
        }
    }

    private void fillApplyStatus(List<HrLendingApplyDTO> records) {
        for (HrLendingApplyDTO applyDTO : records) {
            if (applyDTO.getStatus() == 1) {
                applyDTO.setApplyStatus(0);
            } else if (applyDTO.getStatus() == 3) {
                applyDTO.setApplyStatus(1);
            } else if (applyDTO.getStatus() == 5) {
                applyDTO.setApplyStatus(2);
            } else if (applyDTO.getStatus() == 7) {
                applyDTO.setApplyStatus(3);
            } else if (applyDTO.getStatus() == 9) {
                applyDTO.setApplyStatus(4);
            } else if (applyDTO.getStatus() == 2 || applyDTO.getStatus() == 4 || applyDTO.getStatus() == 6 || applyDTO.getStatus() == 8 || applyDTO.getStatus() == 10) {
                applyDTO.setApplyStatus(5);
            } else {
            }
        }
    }

    @Override
    public String export(HrLendingApplyDTO hrLendingApplyDTO) {
        fillQueryDTO(hrLendingApplyDTO);
        List<HrLendingApplyDTO> list = this.hrLendingApplyRepository.findList(hrLendingApplyDTO);
        List<String> ids = list.stream().map(HrLendingApplyDTO::getId).collect(Collectors.toList());
        fillApplyStatus(list);
        String fileUrl = this.hrAppendixService.uploadExportFile(list, ModuleTypeEnum.ARCHIVES.getValue(), HrLendingApplyDTO.class);
        this.sysOperLogService.insertExportSysOperLog(ModuleTypeEnum.ARCHIVES.getValue(), BusinessTypeEnum.EXPORT.getKey(),
            JSON.toJSONString(ids), ids.size(), fileUrl);
        return fileUrl;
    }

    /**
     * 小程序分页查询借阅申请表
     *
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findWxPage(Long pageNumber, Long pageSize) {
        //查询数据权限
        Page<HrLendingApply> page = new Page<>(pageNumber, pageSize);
        QueryWrapper<HrLendingApply> qw = new QueryWrapper<HrLendingApply>();
        qw.orderByDesc("id");
        IPage iPage = this.hrLendingApplyRepository.selectPage(page, qw);
        List<HrLendingApplyDTO> list = this.hrLendingApplyMapper.toDto(iPage.getRecords());
        Integer[] pass = new Integer[]{1, 3, 5, 7};
        Integer[] npPass = new Integer[]{2, 4, 6, 8};
        Integer[] finish = new Integer[]{9, 10};
        List<Integer> passList = Arrays.asList(pass);
        List<Integer> noPassList = Arrays.asList(npPass);
        List<Integer> finishList = Arrays.asList(finish);
        for (HrLendingApplyDTO hrLendingApplyDTO : list) {
            if (passList.contains(hrLendingApplyDTO.getStatus())) {
                hrLendingApplyDTO.setWxStatus("审核中");
            }
            if (noPassList.contains(hrLendingApplyDTO.getStatus())) {
                hrLendingApplyDTO.setWxStatus("已拒绝");
            }
            if (finishList.contains(hrLendingApplyDTO.getStatus())) {
                hrLendingApplyDTO.setWxStatus("已通过");
            }
        }
        iPage.setRecords(list);
        return iPage;
    }

    //专管员审核
    @Override
    public ResponseEntity auditorReviewLendingApply(BatchOptDTO batchOptDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<String> errorStatusList = new ArrayList<>();//员工状态不匹配集合
        List<String> successList = new ArrayList<>();//成功集合
        for (String lendingApplyId : batchOptDTO.getApplyIdList()) {
            String message = "";
            String msg = "";
            HrLendingApplyDTO hrLendingApplyDTO = hrLendingApplyRepository.getLendingApplyById(lendingApplyId);
            if (hrLendingApplyDTO.getStatus() != LendingApplyEnum.LendingApplyType.TO_BE_MANAGER_REVIEWED.getKey()) {
                errorStatusList.add(hrLendingApplyDTO.getTitle());
                continue;
            }
            JSONObject jsonObject = new JSONObject();
            if (batchOptDTO.getOpt()) {//审批通过
                Integer type = null;
                if (batchOptDTO.getNeedCustomerCheck()) {
                    //需要客户审核
                    hrLendingApplyDTO.setStatus(LendingApplyEnum.LendingApplyType.TO_BE_CUSTOMER_REVIEWED.getKey());
                    jsonObject.put("lendingStatus", LendingApplyEnum.LendingApplyType.TO_BE_CUSTOMER_REVIEWED.getKey());
                    msg = jwtUserDTO.getRealName() + "通过了" + hrLendingApplyDTO.getStaffName() + "的档案借阅申请。等待客户审核";
                    type = 1;
                } else {
                    //不需要客户审核
                    hrLendingApplyDTO.setStatus(LendingApplyEnum.LendingApplyType.TO_BE_FILE_MANAGER_REVIEWED.getKey());
                    jsonObject.put("lendingStatus", LendingApplyEnum.LendingApplyType.TO_BE_FILE_MANAGER_REVIEWED.getKey());
                    msg = jwtUserDTO.getRealName() + "通过了" + hrLendingApplyDTO.getStaffName() + "的档案借阅申请。等待档案管理员审核";
                    type = 2;
                }
                jsonObject.put("message", "专管员已通过您的审核");
                message = jwtUserDTO.getRealName() + "通过了" + hrLendingApplyDTO.getStaffName() + "的档案借阅申请。####" + jsonObject.toJSONString();
                hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.LENDING_APPLY.getKey(), hrLendingApplyDTO.getId(), hrLendingApplyDTO.getStaffId(), String.valueOf(jsonObject.get("message")), false, null);
                hrUpcomingService.createServiceUpcoming(hrLendingApplyDTO.getId(), hrLendingApplyDTO.getStaffId(), "档案借阅-审核" + hrLendingApplyDTO.getStaffName() + "发起的档案借阅申请", LocalDate.now(), type);
            } else {
                jsonObject.put("lendingStatus", LendingApplyEnum.LendingApplyType.MANAGER_REJECTED.getKey());
                jsonObject.put("message", "专管员已拒绝您的审核，拒绝原因：" + batchOptDTO.getCheckerReason() + "如有疑问请联系公司负责人");
                hrLendingApplyDTO.setStatus(LendingApplyEnum.LendingApplyType.MANAGER_REJECTED.getKey());
                msg = jwtUserDTO.getRealName() + "拒绝了" + hrLendingApplyDTO.getStaffName() + "的档案借阅申请。拒绝原因:" + batchOptDTO.getCheckerReason();
                message = msg + "####" + jsonObject.toJSONString();
                hrUpcomingService.updateUpcoming(hrLendingApplyDTO.getId());
            }
            hrLendingApplyRepository.updateById(hrLendingApplyMapper.toEntity(hrLendingApplyDTO));
            successList.add(hrLendingApplyDTO.getTitle());
            //添加操作日志
            String appendixId = null;
            if (batchOptDTO.getAppendixIdList() != null) {
                appendixId = String.join(",", batchOptDTO.getAppendixIdList());
            }
            hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.LENDING_APPLY.getKey(), hrLendingApplyDTO.getId(), hrLendingApplyDTO.getStaffId(), String.valueOf(jsonObject.get("message")), false, null);
            this.hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(lendingApplyId, null, jwtUserDTO.getId(), message, batchOptDTO.getRemark(), false, appendixId, ServiceCenterEnum.LENDING_APPLY.getKey());
            this.hrNotificationUserService.saveRemindContent(hrLendingApplyDTO.getClientId(), ServiceCenterEnum.LENDING_APPLY.getKey(), ServiceCenterEnum.LendingApplyEnum.SPECIAL_SUPERVISOR_REVIEW.getKey(), msg, jwtUserDTO.getId());
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("success", successList);
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(errorStatusList)) {
            jsonObject.put("error_status", "选择的数据中" + String.join(",", errorStatusList) + "的状态值不是待审核");
        }
        return ResponseUtil.buildSuccess(jsonObject);
    }

    //客户PC端审核通过或者拒绝
    @Override
    public ResponseEntity customerReviewLendingApply(BatchOptDTO batchOptDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<String> errorStatusList = new ArrayList<>();//员工状态不匹配集合
        List<String> successList = new ArrayList<>();//成功集合
        for (String lendingApplyId : batchOptDTO.getApplyIdList()) {
            String message = "";
            String msg = "";
            HrLendingApplyDTO hrLendingApplyDTO = hrLendingApplyRepository.getLendingApplyById(lendingApplyId);
            if (hrLendingApplyDTO.getStatus() != LendingApplyEnum.LendingApplyType.TO_BE_CUSTOMER_REVIEWED.getKey()) {
                errorStatusList.add(hrLendingApplyDTO.getTitle());
                continue;
            }
            JSONObject jsonObject = new JSONObject();
            if (batchOptDTO.getOpt()) {//审批通过
                hrLendingApplyDTO.setStatus(LendingApplyEnum.LendingApplyType.TO_BE_FILE_MANAGER_REVIEWED.getKey());
                jsonObject.put("lendingStatus", LendingApplyEnum.LendingApplyType.TO_BE_FILE_MANAGER_REVIEWED.getKey());
                jsonObject.put("message", "公司已通过您的审核");
                message = jwtUserDTO.getRealName() + "客户通过了" + hrLendingApplyDTO.getStaffName() + "的档案借阅申请。####" + jsonObject.toJSONString();
                msg = jwtUserDTO.getRealName() + "客户通过了" + hrLendingApplyDTO.getStaffName() + "的档案借阅申请。等待档案管理员审核";
                hrUpcomingService.createServiceUpcoming(hrLendingApplyDTO.getId(), hrLendingApplyDTO.getStaffId(), "档案借阅-审核" + hrLendingApplyDTO.getStaffName() + "发起的档案借阅申请", LocalDate.now(), 2);
            } else {
                jsonObject.put("lendingStatus", LendingApplyEnum.LendingApplyType.CUSTOMER_REJECTED.getKey());
                jsonObject.put("message", "公司已拒绝您的审核，拒绝原因：" + batchOptDTO.getCheckerReason() + "如有疑问请联系公司负责人");
                hrLendingApplyDTO.setStatus(LendingApplyEnum.LendingApplyType.CUSTOMER_REJECTED.getKey());
                msg = jwtUserDTO.getRealName() + "客户拒绝了" + hrLendingApplyDTO.getStaffName() + "的档案借阅申请。拒绝原因:" + batchOptDTO.getCheckerReason();
                message = msg + "####" + jsonObject.toJSONString();
                hrUpcomingService.updateUpcoming(hrLendingApplyDTO.getId());
            }
            hrLendingApplyRepository.updateById(hrLendingApplyMapper.toEntity(hrLendingApplyDTO));
            successList.add(hrLendingApplyDTO.getTitle());
            //添加操作日志
            String appendixId = null;
            if (batchOptDTO.getAppendixIdList() != null) {
                appendixId = String.join(",", batchOptDTO.getAppendixIdList());
            }
            hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.LENDING_APPLY.getKey(), hrLendingApplyDTO.getId(), hrLendingApplyDTO.getStaffId(), String.valueOf(jsonObject.get("message")), false, null);
            this.hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(lendingApplyId, null, jwtUserDTO.getId(), message, batchOptDTO.getRemark(), false, appendixId, ServiceCenterEnum.LENDING_APPLY.getKey());
            this.hrNotificationUserService.saveRemindContent(hrLendingApplyDTO.getClientId(), ServiceCenterEnum.LENDING_APPLY.getKey(), ServiceCenterEnum.LendingApplyEnum.CUSTOMER_REVIEW.getKey(), msg, jwtUserDTO.getId());
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("success", successList);
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(errorStatusList)) {
            jsonObject.put("error_status", "选择的数据中" + String.join(",", errorStatusList) + "的状态值不是待审核");
        }
        return ResponseUtil.buildSuccess(jsonObject);
    }

    //客服经理PC端审核通过或者拒绝
    @Override
    public ResponseEntity managerReviewLendingApply(BatchOptDTO batchOptDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<String> errorStatusList = new ArrayList<>();//员工状态不匹配集合
        List<String> successList = new ArrayList<>();//成功集合
        for (String lendingApplyId : batchOptDTO.getApplyIdList()) {
            String message = "";
            String msg = "";
            HrLendingApplyDTO hrLendingApplyDTO = hrLendingApplyRepository.getLendingApplyById(lendingApplyId);
            if (hrLendingApplyDTO.getStatus() != LendingApplyEnum.LendingApplyType.TO_BE_CUSTOMER_MANAGER_REVIEWED.getKey()) {
                errorStatusList.add(hrLendingApplyDTO.getTitle());
                continue;
            }
            JSONObject jsonObject = new JSONObject();
            if (batchOptDTO.getOpt()) {//审批通过
                hrLendingApplyDTO.setStatus(LendingApplyEnum.LendingApplyType.TO_BE_FILE_MANAGER_REVIEWED.getKey());
                jsonObject.put("lendingStatus", LendingApplyEnum.LendingApplyType.TO_BE_FILE_MANAGER_REVIEWED.getKey());
                jsonObject.put("message", "经理已通过您的审核");
                message = jwtUserDTO.getRealName() + "经理通过了" + hrLendingApplyDTO.getStaffName() + "的档案借阅申请。####" + jsonObject.toJSONString();
                msg = jwtUserDTO.getRealName() + "经理通过了" + hrLendingApplyDTO.getStaffName() + "的档案借阅申请。等待档案管理员审核";
                hrUpcomingService.createServiceUpcoming(hrLendingApplyDTO.getId(), hrLendingApplyDTO.getStaffId(), "档案借阅-审核" + hrLendingApplyDTO.getStaffName() + "发起的档案借阅申请", LocalDate.now(), 3);
            } else {
                jsonObject.put("lendingStatus", LendingApplyEnum.LendingApplyType.CUSTOMER_MANAGER_REJECTED.getKey());
                jsonObject.put("message", "公司已拒绝您的审核，拒绝原因：" + batchOptDTO.getCheckerReason() + "如有疑问请联系公司负责人");
                hrLendingApplyDTO.setStatus(LendingApplyEnum.LendingApplyType.CUSTOMER_MANAGER_REJECTED.getKey());
                msg = jwtUserDTO.getRealName() + "经理拒绝了" + hrLendingApplyDTO.getStaffName() + "的档案借阅申请。拒绝原因:" + batchOptDTO.getCheckerReason();
                message = msg + "####" + jsonObject.toJSONString();
                hrUpcomingService.updateUpcoming(hrLendingApplyDTO.getId());
            }
            hrLendingApplyRepository.updateById(hrLendingApplyMapper.toEntity(hrLendingApplyDTO));
            successList.add(hrLendingApplyDTO.getTitle());
            //添加操作日志
            String appendixId = null;
            if (batchOptDTO.getAppendixIdList() != null) {
                appendixId = String.join(",", batchOptDTO.getAppendixIdList());
            }
            hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.LENDING_APPLY.getKey(), hrLendingApplyDTO.getId(), hrLendingApplyDTO.getStaffId(), String.valueOf(jsonObject.get("message")), false, null);
            this.hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(lendingApplyId, null, jwtUserDTO.getId(), message, batchOptDTO.getRemark(), false, appendixId, ServiceCenterEnum.LENDING_APPLY.getKey());
            this.hrNotificationUserService.saveRemindContent(hrLendingApplyDTO.getClientId(), ServiceCenterEnum.LENDING_APPLY.getKey(), ServiceCenterEnum.LendingApplyEnum.MANAGER_REVIEW.getKey(), msg, jwtUserDTO.getId());
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("success", successList);
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(errorStatusList)) {
            jsonObject.put("error_status", "选择的数据中" + String.join(",", errorStatusList) + "的状态值不是待审核");
        }
        return ResponseUtil.buildSuccess(jsonObject);
    }

    @Override
    public ResponseEntity archivesManagerReviewLendingApply(BatchOptDTO batchOptDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<String> errorStatusList = new ArrayList<>();//员工状态不匹配集合
        List<String> successList = new ArrayList<>();//成功集合
        for (String lendingApplyId : batchOptDTO.getApplyIdList()) {
            String message = "";
            String msg = "";
            HrLendingApplyDTO hrLendingApplyDTO = hrLendingApplyRepository.getLendingApplyById(lendingApplyId);
            if (hrLendingApplyDTO.getStatus() != LendingApplyEnum.LendingApplyType.TO_BE_FILE_MANAGER_REVIEWED.getKey()) {
                errorStatusList.add(hrLendingApplyDTO.getTitle());
                continue;
            }
            Integer status = 0;
            if (hrLendingApplyDTO.getEndDate() == null && batchOptDTO.getReturnTime() == null) {
                hrLendingApplyDTO.setEndDate(hrLendingApplyDTO.getCreatedDate().plusDays(90).toLocalDate());
            } else {
                hrLendingApplyDTO.setEndDate(batchOptDTO.getReturnTime());
            }
            JSONObject jsonObject = new JSONObject();
            if (batchOptDTO.getOpt()) {//审批通过
                //档案管理员审核时，不需要归还时直接结束，并插入记录，需要归还时，进入待提取
                HrArchivesBringDTO hrArchivesBringDTO = new HrArchivesBringDTO();
                hrArchivesBringDTO.setArchivesId(hrLendingApplyDTO.getManageId());
                hrArchivesBringDTO.setCtType(0);
                hrArchivesBringDTO.setCtProposes(1);
                hrArchivesBringDTO.setCtReasons(hrLendingApplyDTO.getTitle());
                hrArchivesBringDTO.setCtName(hrLendingApplyDTO.getStaffName());
                hrArchivesBringDTO.setCtPhone(hrLendingApplyDTO.getPhone());
                hrArchivesBringDTO.setCtIdCard(hrLendingApplyDTO.getCertificateNum());
                hrArchivesBringDTO.setCtDetail(hrLendingApplyDTO.getDetail());
                hrArchivesBringDTO.setEstimateReturnTime(hrLendingApplyDTO.getEndDate());
                hrArchivesBringDTO.setCtDestination(batchOptDTO.getCalloutPlace());
                hrArchivesBringDTO.setReturnOnTime(hrLendingApplyDTO.getReturnOnTime());
                hrArchivesBringDTO.setIsDelete(false);
                //TODO  根据申请记录ID 查询申请类型id， for循环将archives——detail 改为借阅中  mange表改为借阅中
                //不需要归还时，插入记录，需要归还时进行下一步
                if (batchOptDTO.getNeedReturn()) {
                    hrArchivesBringDTO.setReturnState(0);
                    status = 1;
                    hrLendingApplyDTO.setStatus(LendingApplyEnum.LendingApplyType.TO_BE_PICK_UP.getKey());
                    jsonObject.put("lendingStatus", LendingApplyEnum.LendingApplyType.TO_BE_PICK_UP.getKey());
                    msg = jwtUserDTO.getRealName() + "档案管理员通过了" + hrLendingApplyDTO.getStaffName() + "的档案借阅申请。等待提取";
                    hrUpcomingService.createServiceUpcoming(hrLendingApplyDTO.getId(), hrLendingApplyDTO.getStaffId(), "档案借阅-提取" + hrLendingApplyDTO.getStaffName() + "发起的档案借阅申请", LocalDate.now(), 3);
                } else {
                    hrArchivesBringDTO.setReturnState(0);
                    hrLendingApplyDTO.setStatus(LendingApplyEnum.LendingApplyType.TO_BE_FINISH.getKey());
                    jsonObject.put("lendingStatus", LendingApplyEnum.LendingApplyType.TO_BE_FINISH.getKey());
                    //查询文件明细
                    HrArchivesBring hrArchivesBring = this.hrArchivesBringMapper.toEntity(hrArchivesBringDTO);
                    this.hrArchivesBringRepository.insert(hrArchivesBring);
                    QueryWrapper<HrArchivesManage> queryWrapper = new QueryWrapper();
                    if (hrLendingApplyDTO != null) {
                        if (StringUtils.isNotBlank(hrLendingApplyDTO.getStaffId())) {
                            queryWrapper.eq("staff_id", hrLendingApplyDTO.getStaffId());
                        }
                        if (StringUtils.isNotBlank(hrLendingApplyDTO.getClientId())) {
                            queryWrapper.eq("client_id", hrLendingApplyDTO.getClientId());
                        }
                    }
                    queryWrapper.eq("is_delete", 0);
//                    List<HrArchivesManage> hrArchivesManages = hrArchivesManageRepository.selectList( queryWrapper );
//                    if (hrArchivesManages.size() > 0) {
//                        HrArchivesManage archivesManage = hrArchivesManages.get( 0 );
//                        archivesManage.setArchivesStatus( 2 );
//                        hrArchivesManageRepository.updateById(archivesManage);
//                    }
                    QueryWrapper<HrLendingApplyTpyeRelation> typeQuery = new QueryWrapper<>();
                    typeQuery.eq("lending_apply_id", lendingApplyId);
                    typeQuery.eq("is_delete", 0);
                    List<HrLendingApplyTpyeRelation> tpyeRelationList = hrLendingApplyTpyeRelationRepository.selectList(typeQuery);
                    tpyeRelationList.forEach(ls -> {
                        // 更新档案明细状态
//                        HrArchivesDetailDTO archivesDetail = new HrArchivesDetailDTO();
//                        archivesDetail.setId(ls.getTypeId());
//                        archivesDetail.setState( 2 );
//                        this.hrArchivesDetailRepository.changeDetailState(archivesDetail);
                        // 添加档案明细与调入调出关联
                        ArchivesBringDetail archivesBringDetail = new ArchivesBringDetail();
                        archivesBringDetail.setId(UUID.randomUUID().toString().replace("-", ""));
                        archivesBringDetail.setBringId(hrArchivesBring.getId());
                        archivesBringDetail.setDetailId(ls.getTypeId());
                        this.hrArchivesBringRepository.insertArchivesBringDetail(archivesBringDetail);
                    });
                    hrUpcomingService.updateUpcoming(hrLendingApplyDTO.getId());
                }
                hrLendingApplyDTO.setReturnOnTime(status);
                jsonObject.put("message", "档案管理员已通过您的审核。");
                msg = jwtUserDTO.getRealName() + "档案管理员通过了" + hrLendingApplyDTO.getStaffName() + "的档案借阅申请。";
                message = msg + "####" + jsonObject.toJSONString();
            } else {
                jsonObject.put("lendingStatus", LendingApplyEnum.LendingApplyType.FILE_MANAGER_REJECTED.getKey());
                jsonObject.put("message", "档案管理员已拒绝您的审核，拒绝原因：" + batchOptDTO.getCheckerReason() + "如有疑问请联系公司负责人");
                hrLendingApplyDTO.setStatus(LendingApplyEnum.LendingApplyType.FILE_MANAGER_REJECTED.getKey());
                msg = jwtUserDTO.getRealName() + "档案管理员拒绝了" + hrLendingApplyDTO.getStaffName() + "的档案借阅申请。拒绝原因:" + batchOptDTO.getCheckerReason();
                message = msg + "####" + jsonObject.toJSONString();
                hrUpcomingService.updateUpcoming(hrLendingApplyDTO.getId());
            }
            hrLendingApplyRepository.updateById(hrLendingApplyMapper.toEntity(hrLendingApplyDTO));
            successList.add(hrLendingApplyDTO.getTitle());
            //添加操作日志
            String appendixId = null;
            if (batchOptDTO.getAppendixIdList() != null) {
                appendixId = String.join(",", batchOptDTO.getAppendixIdList());
            }
            LocalDate endDate = hrLendingApplyDTO.getEndDate();
            String value = hrLendingApplyDTO.getType() == 0 ? String.valueOf(jsonObject.get("message")) : hrLendingApplyDTO.getStaffName() + "，你好，请您本人" + (endDate == null ? "尽快" : "在" + endDate) + "前来我公司办理离职档案提取手续。";
            hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.LENDING_APPLY.getKey(), hrLendingApplyDTO.getId(), hrLendingApplyDTO.getStaffId(), value, false, null);
            this.hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(lendingApplyId, null, jwtUserDTO.getId(), message, batchOptDTO.getRemark(), false, appendixId, ServiceCenterEnum.LENDING_APPLY.getKey());
            this.hrNotificationUserService.saveRemindContent(hrLendingApplyDTO.getClientId(), ServiceCenterEnum.LENDING_APPLY.getKey(), ServiceCenterEnum.LendingApplyEnum.ISSUE_ARCHIVIST_CERTIFICATE.getKey(), msg, jwtUserDTO.getId());
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("success", successList);
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(errorStatusList)) {
            jsonObject.put("error_status", "选择的数据中" + String.join(",", errorStatusList) + "的状态值不是待审核");
        }
        return ResponseUtil.buildSuccess(jsonObject);
    }

    @Override
    public ResponseEntity pickUpLendingApply(BatchOptDTO batchOptDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<String> errorStatusList = new ArrayList<>();//员工状态不匹配集合
        List<String> successList = new ArrayList<>();//成功集合
        for (String lendingApplyId : batchOptDTO.getApplyIdList()) {
            String message = "";
            HrLendingApplyDTO hrLendingApplyDTO = hrLendingApplyRepository.getLendingApplyById(lendingApplyId);
            if (hrLendingApplyDTO.getStatus() != LendingApplyEnum.LendingApplyType.TO_BE_PICK_UP.getKey()) {
                errorStatusList.add(hrLendingApplyDTO.getTitle());
                continue;
            }
            if (batchOptDTO.getOpt()) {//审批通过
                hrLendingApplyDTO.setStatus(LendingApplyEnum.LendingApplyType.TO_BE_FINISH.getKey());
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("lendingStatus", LendingApplyEnum.LendingApplyType.TO_BE_FINISH.getKey());
                jsonObject.put("message", "档案已提取");
                message = jwtUserDTO.getRealName() + "已提取了" + hrLendingApplyDTO.getStaffName() + "的档案。####" + jsonObject.toJSONString();
                hrLendingApplyRepository.updateById(hrLendingApplyMapper.toEntity(hrLendingApplyDTO));
                successList.add(hrLendingApplyDTO.getTitle());
                HrLendingApply apply = new HrLendingApply();
                apply.setId(lendingApplyId);
                apply.setCalloutPlace(batchOptDTO.getCalloutPlace());
                hrLendingApplyRepository.updateById(apply);
                HrArchivesBringDTO hrArchivesBringDTO = new HrArchivesBringDTO();
                hrArchivesBringDTO.setArchivesId(hrLendingApplyDTO.getManageId());
                hrArchivesBringDTO.setCtType(0);
                hrArchivesBringDTO.setCtProposes(1);
                hrArchivesBringDTO.setCtReasons(hrLendingApplyDTO.getTitle());
                hrArchivesBringDTO.setCtName(hrLendingApplyDTO.getStaffName());
                hrArchivesBringDTO.setCtPhone(hrLendingApplyDTO.getPhone());
                hrArchivesBringDTO.setCtIdCard(hrLendingApplyDTO.getCertificateNum());
                hrArchivesBringDTO.setCtDetail(hrLendingApplyDTO.getDetail());
                hrArchivesBringDTO.setCtDestination(batchOptDTO.getCalloutPlace());
                hrArchivesBringDTO.setEstimateReturnTime(hrLendingApplyDTO.getEndDate());
                hrArchivesBringDTO.setReturnOnTime(hrLendingApplyDTO.getReturnOnTime());
                hrArchivesBringDTO.setReturnState(1);
                hrArchivesBringDTO.setIsDelete(false);
                HrArchivesBring hrArchivesBring = this.hrArchivesBringMapper.toEntity(hrArchivesBringDTO);
                this.hrArchivesBringRepository.insert(hrArchivesBring);
                //查询文件明细
                QueryWrapper<HrArchivesManage> queryWrapper = new QueryWrapper();
                if (hrLendingApplyDTO != null) {
                    if (StringUtils.isNotBlank(hrLendingApplyDTO.getStaffId())) {
                        queryWrapper.eq("staff_id", hrLendingApplyDTO.getStaffId());
                    }
                    if (StringUtils.isNotBlank(hrLendingApplyDTO.getClientId())) {
                        queryWrapper.eq("client_id", hrLendingApplyDTO.getClientId());
                    }
                }
                queryWrapper.eq("is_delete", 0);
                List<HrArchivesManage> hrArchivesManages = hrArchivesManageRepository.selectList(queryWrapper);
                if (hrArchivesManages.size() > 0) {
                    HrArchivesManage archivesManage = hrArchivesManages.get(0);
                    archivesManage.setArchivesStatus(2);
                    hrArchivesManageRepository.updateById(archivesManage);
                }
                QueryWrapper<HrLendingApplyTpyeRelation> typeQuery = new QueryWrapper<>();
                typeQuery.eq("lending_apply_id", lendingApplyId);
                typeQuery.eq("is_delete", 0);
                List<HrLendingApplyTpyeRelation> tpyeRelationList = hrLendingApplyTpyeRelationRepository.selectList(typeQuery);
                tpyeRelationList.forEach(ls -> {
                    // 更新档案明细状态
                    HrArchivesDetailDTO archivesDetail = new HrArchivesDetailDTO();
                    archivesDetail.setId(ls.getTypeId());
                    archivesDetail.setState(2);
                    this.hrArchivesDetailRepository.changeDetailState(archivesDetail);
                    // 添加档案明细与调入调出关联
                    ArchivesBringDetail archivesBringDetail = new ArchivesBringDetail();
                    archivesBringDetail.setId(UUID.randomUUID().toString().replace("-", ""));
                    archivesBringDetail.setBringId(hrArchivesBring.getId());
                    archivesBringDetail.setDetailId(ls.getTypeId());
                    this.hrArchivesBringRepository.insertArchivesBringDetail(archivesBringDetail);
                });
                //添加操作日志
                String appendixId = null;
                if (batchOptDTO.getAppendixIdList() != null) {
                    appendixId = String.join(",", batchOptDTO.getAppendixIdList());
                }
                hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.LENDING_APPLY.getKey(), hrLendingApplyDTO.getId(), hrLendingApplyDTO.getStaffId(), String.valueOf(jsonObject.get("message")), false, null);
                this.hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(lendingApplyId, null, jwtUserDTO.getId(), message, batchOptDTO.getRemark(), false, appendixId, ServiceCenterEnum.LENDING_APPLY.getKey());
                this.hrNotificationUserService.saveRemindContent(hrLendingApplyDTO.getClientId(), ServiceCenterEnum.LENDING_APPLY.getKey(), ServiceCenterEnum.LendingApplyEnum.TO_BE_EXTRACTED.getKey(), jwtUserDTO.getRealName() + "已提取了" + hrLendingApplyDTO.getStaffName() + "的档案", jwtUserDTO.getId());
                hrUpcomingService.updateUpcoming(hrLendingApplyDTO.getId());
            }
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("success", successList);
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(errorStatusList)) {
            jsonObject.put("error_status", "选择的数据中" + String.join(",", errorStatusList) + "的状态值不是待审核");
        }
        return ResponseUtil.buildSuccess(jsonObject);
    }

    @Override
    public List<HrLendingApplyDTO> getLendingApply() {
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        //根据员工id获取所有工伤服务的id
        QueryWrapper<HrLendingApply> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("staff_id", jwtMiniDTO.getId());
        queryWrapper.eq("is_delete", 0);
        List<HrLendingApply> list = list(queryWrapper);
        ArrayList<HrLendingApplyDTO> hrLendingApplys = new ArrayList<>();
        for (HrLendingApply hrLendingApply : list) {
            //获取每一个的日志
            HrLendingApplyDTO hrLendingApplyDTO = hrLendingApplyRepository.getLendingApplyById(hrLendingApply.getId());
            List<HrApplyOpLogsDTO> applyOpLogsList = hrApplyOpLogsService.findApplyOpLogsList(hrLendingApply.getId(), null);
            hrLendingApplyDTO.setOpLogsList(applyOpLogsList);
            hrLendingApplys.add(hrLendingApplyDTO);
        }
        return hrLendingApplys;
    }

    //批量审核操作
    @Override
    public ResponseEntity batchPassLendingApply(BatchOptDTO batchOptDTO) {
        JWTUserDTO jwtUserDTO = SecurityUtils.getCurrentUser().get();
        List<String> errorStatusAuditorList = new ArrayList<>();//状态值不是待专管员审核的错误集合
        List<String> errorStatusCustomerList = new ArrayList<>();//状态值不是待客户审核的错误集合
        List<String> errorStatusManagerList = new ArrayList<>();//状态值不是待服务经理审核的错误集合
        List<String> errorStatusFileManagerList = new ArrayList<>();//状态值不是待档案管理员审核的错误集合
        List<String> errorStatusList = new ArrayList<>();//状态值错误集合
        List<String> successList = new ArrayList<>();//成功集合
        for (String applyId : batchOptDTO.getApplyIdList()) {
            String message = "";
            String roleName = "";
            Integer applyStatus = null;
            String content = "";
            Integer launchType = null;
            String msg = "";
            Integer type = null;
            HrLendingApplyDTO hrLendingApplyDTO = hrLendingApplyRepository.getLendingApplyById(applyId);
            //判断角色
            switch (batchOptDTO.getRoleId()) {
                case 0://待专管员审核
                    roleName = "专管员";
                    launchType = ServiceCenterEnum.LendingApplyEnum.SPECIAL_SUPERVISOR_REVIEW.getKey();
                    if (hrLendingApplyDTO.getStatus() != LendingApplyEnum.LendingApplyType.TO_BE_MANAGER_REVIEWED.getKey()) {
                        errorStatusAuditorList.add(hrLendingApplyDTO.getTitle());
                        continue;
                    } else {
                        if (batchOptDTO.getOpt()) {
                            if (batchOptDTO.getNeedCustomerCheck()) {
                                msg = "等待客户审核";
                                type = 1;
                                applyStatus = LendingApplyEnum.LendingApplyType.TO_BE_CUSTOMER_REVIEWED.getKey();
                            } else {
                                msg = "等待档案管理员审核";
                                type = 3;
                                applyStatus = LendingApplyEnum.LendingApplyType.TO_BE_FILE_MANAGER_REVIEWED.getKey();
                            }
                        } else {
                            applyStatus = LendingApplyEnum.LendingApplyType.MANAGER_REJECTED.getKey();
                        }
                    }
                    break;
                case 1://客户
                    roleName = "客户";
                    launchType = ServiceCenterEnum.LendingApplyEnum.CUSTOMER_REVIEW.getKey();
                    if (hrLendingApplyDTO.getStatus() != LendingApplyEnum.LendingApplyType.TO_BE_CUSTOMER_REVIEWED.getKey()) {
                        errorStatusCustomerList.add(hrLendingApplyDTO.getTitle());
                        continue;
                    } else {
                        if (batchOptDTO.getOpt()) {
                            msg = "等待档案管理员审核";
                            type = 3;
                            applyStatus = LendingApplyEnum.LendingApplyType.TO_BE_FILE_MANAGER_REVIEWED.getKey();
                        } else {
                            applyStatus = LendingApplyEnum.LendingApplyType.CUSTOMER_REJECTED.getKey();
                        }
                    }
                    break;
                /*case 2:
                    roleName = "客户经理";
                    launchType = ServiceCenterEnum.LendingApplyEnum.MANAGER_REVIEW.getKey();

                    if (hrLendingApplyDTO.getStatus() != LendingApplyEnum.LendingApplyType.TO_BE_CUSTOMER_MANAGER_REVIEWED.getKey()) {
                        errorStatusManagerList.add( hrLendingApplyDTO.getTitle() );
                        continue;
                    } else {
                        if (batchOptDTO.getOpt()) {
                            msg = "等待档案管理员审核";
                            type = 3;
                            applyStatus = LendingApplyEnum.LendingApplyType.TO_BE_FILE_MANAGER_REVIEWED.getKey();
                        } else {
                            applyStatus = LendingApplyEnum.LendingApplyType.CUSTOMER_MANAGER_REJECTED.getKey();
                        }
                    }
                    break;*/
                case 3:
                    roleName = "档案管理员";
                    launchType = ServiceCenterEnum.LendingApplyEnum.ISSUE_ARCHIVIST_CERTIFICATE.getKey();
                    if (hrLendingApplyDTO.getStatus() != LendingApplyEnum.LendingApplyType.TO_BE_FILE_MANAGER_REVIEWED.getKey()) {
                        errorStatusFileManagerList.add(hrLendingApplyDTO.getTitle());
                        continue;
                    } else {
                        if (batchOptDTO.getOpt()) {
                            //批量通过 默认是不需要归还
                            applyStatus = LendingApplyEnum.LendingApplyType.TO_BE_FINISH.getKey();
                        } else {
                            applyStatus = LendingApplyEnum.LendingApplyType.FILE_MANAGER_REJECTED.getKey();
                        }
                    }
                    break;
                default:
                    if (hrLendingApplyDTO.getStatus() == LendingApplyEnum.LendingApplyType.TO_BE_MANAGER_REVIEWED.getKey()) {
                        launchType = ServiceCenterEnum.LendingApplyEnum.SPECIAL_SUPERVISOR_REVIEW.getKey();
                        type = 1;
                        if (batchOptDTO.getOpt()) {
                            if (batchOptDTO.getNeedCustomerCheck()) {
                                msg = "等待客户审核";
                                applyStatus = LendingApplyEnum.LendingApplyType.TO_BE_CUSTOMER_REVIEWED.getKey();
                            } else {
                                msg = "等待档案管理员审核";
                                applyStatus = LendingApplyEnum.LendingApplyType.TO_BE_FILE_MANAGER_REVIEWED.getKey();
                            }
                        } else {
                            applyStatus = LendingApplyEnum.LendingApplyType.MANAGER_REJECTED.getKey();
                        }
                    } else if (hrLendingApplyDTO.getStatus() == LendingApplyEnum.LendingApplyType.TO_BE_CUSTOMER_REVIEWED.getKey()) {
                        msg = "等待档案管理员审核";
                        type = 2;
                        launchType = ServiceCenterEnum.LendingApplyEnum.CUSTOMER_REVIEW.getKey();
                        if (batchOptDTO.getOpt()) {
                            applyStatus = LendingApplyEnum.LendingApplyType.TO_BE_FILE_MANAGER_REVIEWED.getKey();
                        } else {
                            applyStatus = LendingApplyEnum.LendingApplyType.CUSTOMER_REJECTED.getKey();
                        }
                    }
                    /*else if (hrLendingApplyDTO.getStatus() == LendingApplyEnum.LendingApplyType.TO_BE_CUSTOMER_MANAGER_REVIEWED.getKey()) {
                        launchType = ServiceCenterEnum.LendingApplyEnum.MANAGER_REVIEW.getKey();
                        msg = "等待档案管理员审核";
                        type = 3;
                        if (batchOptDTO.getOpt()) {
                            applyStatus = LendingApplyEnum.LendingApplyType.TO_BE_FILE_MANAGER_REVIEWED.getKey();
                        } else {
                            applyStatus = LendingApplyEnum.LendingApplyType.CUSTOMER_MANAGER_REJECTED.getKey();
                        }
                    } */
                    else if (hrLendingApplyDTO.getStatus() == LendingApplyEnum.LendingApplyType.TO_BE_FILE_MANAGER_REVIEWED.getKey()) {
                        launchType = ServiceCenterEnum.LendingApplyEnum.ISSUE_ARCHIVIST_CERTIFICATE.getKey();
                        msg = "流程结束";
                        if (batchOptDTO.getOpt()) {
                            applyStatus = LendingApplyEnum.LendingApplyType.TO_BE_FINISH.getKey();
                        } else {
                            applyStatus = LendingApplyEnum.LendingApplyType.FILE_MANAGER_REJECTED.getKey();
                        }
                    } else {
                        errorStatusList.add(hrLendingApplyDTO.getTitle());
                        continue;
                    }
                    break;
            }
            hrLendingApplyDTO.setStatus(applyStatus);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("lendingStatus", applyStatus);
            if (batchOptDTO.getOpt()) {//审批通过
                jsonObject.put("message", roleName + "已通过您的审核");
                content = jwtUserDTO.getRealName() + "通过了" + hrLendingApplyDTO.getStaffName() + "的档案借阅申请。" + msg;
                message = jwtUserDTO.getRealName() + "通过了" + hrLendingApplyDTO.getStaffName() + "的档案借阅申请。####" + jsonObject.toJSONString();
                if (type != null) {
                    hrUpcomingService.createServiceUpcoming(hrLendingApplyDTO.getId(), hrLendingApplyDTO.getStaffId(), "档案借阅-审核" + hrLendingApplyDTO.getStaffName() + "发起的档案借阅申请", LocalDate.now(), type);
                } else {
                    hrUpcomingService.updateUpcoming(hrLendingApplyDTO.getId());
                }
            } else {
                jsonObject.put("message", roleName + "已拒绝您的审核");
                message = jwtUserDTO.getRealName() + "拒绝了" + hrLendingApplyDTO.getStaffName() + "的档案借阅申请。";
                if (StringUtils.isNotBlank(batchOptDTO.getCheckerReason())) {
                    message = message + "拒绝原因:" + batchOptDTO.getCheckerReason();
                }
                content = message;
                message = message + "####" + jsonObject.toJSONString();
                hrUpcomingService.updateUpcoming(hrLendingApplyDTO.getId());
            }
            hrLendingApplyRepository.updateById(hrLendingApplyMapper.toEntity(hrLendingApplyDTO));
            successList.add(hrLendingApplyDTO.getTitle());
            this.hrApplyOpLogsService.saveHrApplyOpLogsEnclosure(applyId, null, jwtUserDTO.getId(), message, batchOptDTO.getRemark(), false, null, ServiceCenterEnum.LENDING_APPLY.getKey());
            hrAppletMessageService.updateNoticeMessage(ServiceCenterEnum.LENDING_APPLY.getKey(), hrLendingApplyDTO.getId(), hrLendingApplyDTO.getStaffId(), String.valueOf(jsonObject.get("message")), false, null);
            this.hrNotificationUserService.saveRemindContent(hrLendingApplyDTO.getClientId(), ServiceCenterEnum.LENDING_APPLY.getKey(), launchType, content, jwtUserDTO.getId());

            //如果是档案管理员审核，并且不需要归还，则记录
            if (hrLendingApplyDTO.getStatus() == LendingApplyEnum.LendingApplyType.TO_BE_FILE_MANAGER_REVIEWED.getKey()) {
                if (batchOptDTO.getOpt()) {
                    //批量通过 默认是不需要归还
                    HrArchivesBringDTO hrArchivesBringDTO = new HrArchivesBringDTO();
                    hrArchivesBringDTO.setArchivesId(hrLendingApplyDTO.getManageId());
                    hrArchivesBringDTO.setCtType(0);
                    hrArchivesBringDTO.setCtProposes(1);
                    hrArchivesBringDTO.setCtReasons(hrLendingApplyDTO.getTitle());
                    hrArchivesBringDTO.setCtName(hrLendingApplyDTO.getStaffName());
                    hrArchivesBringDTO.setCtPhone(hrLendingApplyDTO.getPhone());
                    hrArchivesBringDTO.setCtIdCard(hrLendingApplyDTO.getCertificateNum());
                    hrArchivesBringDTO.setCtDetail(hrLendingApplyDTO.getDetail());
                    hrArchivesBringDTO.setEstimateReturnTime(hrLendingApplyDTO.getEndDate());
                    hrArchivesBringDTO.setCtDestination(batchOptDTO.getCalloutPlace());
                    hrArchivesBringDTO.setReturnOnTime(hrLendingApplyDTO.getReturnOnTime());
                    hrArchivesBringDTO.setIsDelete(false);
                    hrArchivesBringDTO.setReturnState(0);
                    //查询文件明细
                    HrArchivesBring hrArchivesBring = this.hrArchivesBringMapper.toEntity(hrArchivesBringDTO);
                    this.hrArchivesBringRepository.insert(hrArchivesBring);
                    QueryWrapper<HrArchivesManage> queryWrapper = new QueryWrapper();
                    if (hrLendingApplyDTO != null) {
                        if (StringUtils.isNotBlank(hrLendingApplyDTO.getStaffId())) {
                            queryWrapper.eq("staff_id", hrLendingApplyDTO.getStaffId());
                        }
                        if (StringUtils.isNotBlank(hrLendingApplyDTO.getClientId())) {
                            queryWrapper.eq("client_id", hrLendingApplyDTO.getClientId());
                        }
                    }
                    queryWrapper.eq("is_delete", 0);
                    QueryWrapper<HrLendingApplyTpyeRelation> typeQuery = new QueryWrapper<>();
                    typeQuery.eq("lending_apply_id", applyId);
                    typeQuery.eq("is_delete", 0);
                    List<HrLendingApplyTpyeRelation> tpyeRelationList = hrLendingApplyTpyeRelationRepository.selectList(typeQuery);
                    tpyeRelationList.forEach(ls -> {
                        // 添加档案明细与调入调出关联
                        ArchivesBringDetail archivesBringDetail = new ArchivesBringDetail();
                        archivesBringDetail.setId(UUID.randomUUID().toString().replace("-", ""));
                        archivesBringDetail.setBringId(hrArchivesBring.getId());
                        archivesBringDetail.setDetailId(ls.getTypeId());
                        this.hrArchivesBringRepository.insertArchivesBringDetail(archivesBringDetail);
                    });
                }
            }
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("success", successList);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(errorStatusList)) {
            jsonObject.put("error_status", "选择的数据中" + String.join(",", errorStatusList) + "不是需要审核的流程");
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(errorStatusAuditorList)) {
            jsonObject.put("error_status_auditor", "选择的数据中" + String.join(",", errorStatusAuditorList) + "不是需要专管员审核的流程");
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(errorStatusCustomerList)) {
            jsonObject.put("error_status_customer", "选择的数据中" + String.join(",", errorStatusCustomerList) + "不是需要客户审核的流程");
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(errorStatusManagerList)) {
            jsonObject.put("error_status_manager", "选择的数据中" + String.join(",", errorStatusManagerList) + "不是需要经理审核的流程");
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(errorStatusFileManagerList)) {
            jsonObject.put("error_status_file_manager", "选择的数据中" + String.join(",", errorStatusFileManagerList) + "不是需要档案管理员审核的流程");
        }
        return ResponseUtil.buildSuccess(jsonObject);
    }

    @Override
    public List<HrLendingApplyDTO> findWxNoPage() {
        Page<HrLendingApply> page = new Page<>(1, 1000);
        JWTMiniDTO jwtMiniDTO = SecurityUtils.getCurrentMini().get();
        QueryWrapper<HrLendingApply> qw = new QueryWrapper<HrLendingApply>();
        qw.eq("staff_id", jwtMiniDTO.getId());
        qw.eq("is_delete", 0);
        qw.orderByDesc("created_date");
        IPage iPage = this.hrLendingApplyRepository.selectPage(page, qw);
        List<HrLendingApplyDTO> list = this.hrLendingApplyMapper.toDto(iPage.getRecords());
        Integer[] pass = new Integer[]{1, 3, 5, 7};
        Integer[] npPass = new Integer[]{2, 4, 6, 8};
        Integer[] finish = new Integer[]{9, 10};
        List<Integer> passList = Arrays.asList(pass);
        List<Integer> noPassList = Arrays.asList(npPass);
        List<Integer> finishList = Arrays.asList(finish);
        for (HrLendingApplyDTO hrLendingApplyDTO : list) {
            if (passList.contains(hrLendingApplyDTO.getStatus())) {
                hrLendingApplyDTO.setWxStatus("审核中");
            }
            if (noPassList.contains(hrLendingApplyDTO.getStatus())) {
                hrLendingApplyDTO.setWxStatus("已拒绝");
            }
            if (finishList.contains(hrLendingApplyDTO.getStatus())) {
                hrLendingApplyDTO.setWxStatus("已通过");
            }
            List<HrArchivesDetailDTO> detailDTOList = new ArrayList<>();
            String typeListStr = hrLendingApplyDTO.getTypeListStr();
            if (typeListStr.contains("线下提档")){
                detailDTOList.add(new HrArchivesDetailDTO().setName("线下提档").setType(1));
            }
            List<HrLendingApplyTpyeRelation> relationList = hrLendingApplyTpyeRelationRepository.selectList(new QueryWrapper<HrLendingApplyTpyeRelation>().eq("lending_apply_id",hrLendingApplyDTO.getId()));
            if (relationList != null && !relationList.isEmpty()){
                String idsStr = relationList.stream().map(HrLendingApplyTpyeRelation::getTypeId).collect(Collectors.joining(","));
                List<HrArchivesDetailDTO> archivesDetailList = hrArchivesDetailRepository.getDetailByDetailIds(idsStr);
                detailDTOList.addAll(archivesDetailList);
            }
            hrLendingApplyDTO.setArchivesDetailList(detailDTOList);
        }
        return list;
    }

    @Override
    public User getAuditorInfo(String id) {
        User user = userService.getAuditorInfo(id);
        return user;
    }

    @Override
    public List<HrArchivesDetailDTO> getBorrowingMaterials(String id) {
        List<HrLendingApplyTpyeRelation> lendingApplyTpyeRelationList = hrLendingApplyTpyeRelationRepository.selectList(new QueryWrapper<HrLendingApplyTpyeRelation>().eq("lending_apply_id", id));
        if (CollectionUtils.isNotEmpty(lendingApplyTpyeRelationList)) {
            List<String> typeIds = lendingApplyTpyeRelationList.stream().map(HrLendingApplyTpyeRelation::getTypeId).collect(Collectors.toList());
            String idsStr = typeIds.stream()
                .collect(Collectors.joining(","));
            List<HrArchivesDetailDTO> list = hrArchivesDetailRepository.getDetailByDetailIds(idsStr);
            return list;
        }
        return null;
    }

    /**
     * 查询员工对应的档案明细
     *
     * @param staffId 员工ID
     * @return
     */
    @Override
    public List<HrArchivesDetail> findDetailStaff(String staffId) {
        return hrArchivesDetailRepository.getDealByStaffId(staffId);
    }
}
