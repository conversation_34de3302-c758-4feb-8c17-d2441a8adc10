package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.common.Constants;
import cn.casair.common.enums.*;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.*;
import cn.casair.common.utils.excel.ZlSheet;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.dto.billl.BillReimApplyUrgeDTO;
import cn.casair.dto.nc.param.AuxiliaryDTO;
import cn.casair.dto.nc.param.VoucherDetailDTO;
import cn.casair.dto.nc.param.VoucherHeadDTO;
import cn.casair.dto.nc.result.AuxiliaryResultDTO;
import cn.casair.dto.nc.result.NcResultDTO;
import cn.casair.mapper.HrBillReimbursementApplyDetailMapper;
import cn.casair.mapper.HrBillReimbursementApplyMapper;
import cn.casair.mapper.HrBillReimbursementClientMapper;
import cn.casair.repository.*;
import cn.casair.service.*;
import cn.casair.service.util.SecurityUtils;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.spire.xls.FileFormat;
import com.spire.xls.Workbook;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报销申请服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-17
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class HrBillReimbursementApplyServiceImpl extends ServiceImpl<HrBillReimbursementApplyRepository, HrBillReimbursementApply> implements HrBillReimbursementApplyService {

    /**
     * 审核通过
     */
    private static final Integer AUDIT_SUCCESS = 1;

    /**
     * 审核拒绝
     */
    private static final Integer AUDIT_REJECT = 2;

    /**
     * 会计确认
     */
    private static final Integer AUDIT_CONFIRM = 3;

    private static final String MESSAGE_AUDIT_SUCCESS = "审核通过!";

    private static final String MESSAGE_AUDIT_REJECT = "审核拒绝!";

    /**
     * 通知标题最大长度
     */
    private static final int NOTICE_MSG_MAX_LENGTH = 20;


    private final SysOperLogService sysOperLogService;
    private final HrBillReimbursementApplyRepository hrBillReimbursementApplyRepository;
    private final HrBillReimbursementApplyMapper hrBillReimbursementApplyMapper;
    private final HrBillReimbursementApplyDetailRepository hrBillReimbursementApplyDetailRepository;
    private final HrBillReimbursementApplyDetailService hrBillReimbursementApplyDetailService;
    private final HrBillReimbursementApplyDetailMapper hrBillReimbursementApplyDetailMapper;
    private final HrAppendixService hrAppendixService;
    private final HrApplyOpLogsService hrApplyOpLogsService;
    private final UserRepository userRepository;
    private final HrMessageListService hrMessageListService;
    private final RoleRepository roleRepository;
    private final HrBillTotalRepository hrBillTotalRepository;
    private final HrClientService hrClientService;
    private final HrFeeReviewRepository hrFeeReviewRepository;
    private final HrBillRepository hrBillRepository;
    private final HrApplyOpLogsRepository hrApplyOpLogsRepository;
    private final HrBillDetailRepository hrBillDetailRepository;
    private final HrPlatformAccountRepository platformAccountRepository;
    private final HrBillReimbursementClientMapper hrBillReimbursementClientMapper;
    private final HrBillReimbursementClientRepository hrBillReimbursementClientRepository;
    private final HrBillReimbursementClientService hrBillReimbursementClientService;
    private final HrClientRepository hrClientRepository;
    private final NcAccountRepository ncAccountRepository;
    private final NcCustomerRepository ncCustomerRepository;
    private final NcService ncService;

    @Value("${file.temp-path}")
    private String tempPath;
    @Value("${constant.fontPath}")
    private String fontPath;
    @Value("${nc.accountbook}")
    private String ncAccountbook;

    /**
     * 创建报销申请
     *
     * @param hrBillReimbursementApplyDTO
     * @return
     */
    @Override
    public HrBillReimbursementApplyDTO createHrBillReimbursementApply(HrBillReimbursementApplyDTO hrBillReimbursementApplyDTO) {
        log.info("Create new HrBillReimbursementApply:{}", hrBillReimbursementApplyDTO);

        // 校验流程状态
        if (!BillReimbApproveEnums.NOT_LAUNCH.getKey().equals(hrBillReimbursementApplyDTO.getApproveStatus()) &&
            !BillReimbApproveEnums.ON_CUSTOMER_MANAGER.getKey().equals(hrBillReimbursementApplyDTO.getApproveStatus())) {
            throw new CommonException("异常操作,创建申请失败!");
        }

        JWTUserDTO curUser = SecurityUtils.getJwtUser();
        if (hrBillReimbursementApplyDTO.getFlag()) {
            hrBillReimbursementApplyDTO.setApplyUserId(curUser.getId());
        }

        List<HrBillReimbursementApplyDetailDTO> detailDTOList = hrBillReimbursementApplyDTO.getDetailDTOList();
        if (detailDTOList == null || detailDTOList.isEmpty()) {
            throw new CommonException("费用明细不能为空!");
        }
        hrBillReimbursementApplyDTO.setLastModifiedDate(LocalDateTime.now());
        HrBillReimbursementApply hrBillReimbursementApply = this.hrBillReimbursementApplyMapper.toEntity(hrBillReimbursementApplyDTO);
        this.hrBillReimbursementApplyRepository.insert(hrBillReimbursementApply);

        // 保存费用项明细和附件明细
        this.saveFeeDetailAndEnclosures(hrBillReimbursementApply.getId(), detailDTOList, hrBillReimbursementApplyDTO.getEnclosures());

        // 如果发起了申请则保存日志
        if (BillReimbApproveEnums.ON_CUSTOMER_MANAGER.getKey().equals(hrBillReimbursementApply.getApproveStatus())) {
            this.saveApplyOpLogs(hrBillReimbursementApply.getId(), hrBillReimbursementApply.getApplyUserId(), curUser.getId(),
                "发起申请", "", false);
        }
        List<HrAppendixDTO> hrAppendixListByIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(hrBillReimbursementApplyDTO.getEnclosures())) {
            List<String> collect = hrBillReimbursementApplyDTO.getEnclosures().stream().map(HrAppendixDTO::getId).collect(Collectors.toList());
            if (!collect.isEmpty()) {
                hrAppendixListByIds = this.hrAppendixService.getHrAppendixListByIds(collect);
            }
        }
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.REIMBURSEMENT_APPLY.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrBillReimbursementApplyDTO),
            HrBillReimbursementApplyDTO.class,
            hrAppendixListByIds.stream().map(HrAppendixDTO::getFileUrl).collect(Collectors.joining(",")),
            JSON.toJSONString(hrBillReimbursementApply)
        );

        return this.hrBillReimbursementApplyMapper.toDto(hrBillReimbursementApply);
    }

    /**
     * 可申请报销发起申请
     *
     * @param hrBillReimbursementApplyDTO
     * @return
     */
    @Override
    public HrBillReimbursementApplyDTO saveHrBillReimbursementApply(HrBillReimbursementApplyDTO hrBillReimbursementApplyDTO) {
        boolean flag = false;
        List<HrBillReimbursementApplyDetailDTO> detailDTOList = hrBillReimbursementApplyDTO.getDetailDTOList();
        List<HrBillReimbursementApplyDetailDTO> checkOnDetailDtoList = detailDTOList.stream().filter(lst -> lst.getCheckOn() != null && lst.getCheckOn() == 1).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(checkOnDetailDtoList)) {
            throw new CommonException("选中的明细为空！");
        }
        List<Integer> stateList = checkOnDetailDtoList.stream().map(HrBillReimbursementApplyDetailDTO::getState).distinct().collect(Collectors.toList());
        if (stateList.size() == 1) {
            Integer state = stateList.get(0);
            if (state.equals(BillInvoiceApproveEnums.InvoiceRecordState.NOT_LOCKED.getKey())) {//选中明细第一次发起暂存
                this.saveBillReimbursementApplyDetail(hrBillReimbursementApplyDTO, checkOnDetailDtoList, false);
            } else if (state.equals(BillInvoiceApproveEnums.InvoiceRecordState.UNLOCK.getKey())) {
                //特殊客户报销重新发起会生成新的报销记录
                if (hrBillReimbursementApplyDTO.getAccountType() != null && hrBillReimbursementApplyDTO.getAccountType().equals(BillReimbApproveEnums.InvoiceTypeEnum.SPECIAL_CLIENT.getKey())) {
                    this.saveBillReimbursementApplyDetail(hrBillReimbursementApplyDTO, checkOnDetailDtoList, false);
                } else {
                    List<String> collect = checkOnDetailDtoList.stream().map(HrBillReimbursementApplyDetailDTO::getLevelId).collect(Collectors.toList());
                    List<String> applyIds = hrBillReimbursementApplyDetailRepository.selectBatchIds(collect)
                        .stream().map(HrBillReimbursementApplyDetail::getApplyId).distinct().collect(Collectors.toList());
                    List<HrBillReimbursementApplyDetail> list = hrBillReimbursementApplyDetailRepository.selectList(new QueryWrapper<HrBillReimbursementApplyDetail>().in("apply_id", applyIds));
                    //判断选中的明细是否在同一报销记录中
                    if (applyIds.size() == 1 && list.size() == checkOnDetailDtoList.size()) {
                        if (hrBillReimbursementApplyDTO.getAccountType() != null && hrBillReimbursementApplyDTO.getAccountType().equals(BillReimbApproveEnums.InvoiceTypeEnum.SPECIAL_CLIENT.getKey())) {
                            hrBillReimbursementApplyDetailRepository.updateState(collect, BillInvoiceApproveEnums.InvoiceRecordState.AGAIN_LOCKED.getKey());
                            this.saveBillReimbursementApplyDetail(hrBillReimbursementApplyDTO, checkOnDetailDtoList, false);
                        } else {
                            this.updateHrBillReimbursementApplyDetail(hrBillReimbursementApplyDTO, applyIds.get(0), checkOnDetailDtoList);
                            flag = true;
                        }
                    } else {//否 重新发起记录 将上一次的记录明细状态改为 重新锁定
                        hrBillReimbursementApplyDetailRepository.updateState(collect, BillInvoiceApproveEnums.InvoiceRecordState.AGAIN_LOCKED.getKey());
                        this.saveBillReimbursementApplyDetail(hrBillReimbursementApplyDTO, checkOnDetailDtoList, false);
                    }
                }
            }
        } else {
            if (hrBillReimbursementApplyDTO.getAccountType() == null
                || (hrBillReimbursementApplyDTO.getAccountType() != null && !hrBillReimbursementApplyDTO.getAccountType().equals(BillReimbApproveEnums.InvoiceTypeEnum.SPECIAL_CLIENT.getKey()))) {
                List<String> collect = detailDTOList.stream().filter(ls -> ls.getState().equals(BillInvoiceApproveEnums.InvoiceRecordState.UNLOCK.getKey()))
                    .map(HrBillReimbursementApplyDetailDTO::getLevelId).collect(Collectors.toList());
                hrBillReimbursementApplyDetailRepository.updateState(collect, BillInvoiceApproveEnums.InvoiceRecordState.AGAIN_LOCKED.getKey());
            }
            this.saveBillReimbursementApplyDetail(hrBillReimbursementApplyDTO, checkOnDetailDtoList, false);
        }
        //修改可申请报销锁定状态
        QueryWrapper<HrBillReimbursementApplyDetail> qw = new QueryWrapper<>();
        qw.eq("apply_id", hrBillReimbursementApplyDTO.getId());
        List<HrBillReimbursementApplyDetail> hrBillReimbursementApplyDetails = hrBillReimbursementApplyDetailRepository.selectList(qw);
        List<HrBillReimbursementApplyDetail> notLockedRecordList = hrBillReimbursementApplyDetails.stream().filter(ls -> ls.getState().equals(BillInvoiceApproveEnums.InvoiceRecordState.NOT_LOCKED.getKey())
            || ls.getState().equals(BillInvoiceApproveEnums.InvoiceRecordState.UNLOCK.getKey())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notLockedRecordList)) {
            hrBillReimbursementApplyDTO.setReimbursementLockState(BillInvoiceApproveEnums.InvoiceLockState.LOCKED.getKey());
        } else {
            hrBillReimbursementApplyDTO.setReimbursementLockState(BillInvoiceApproveEnums.InvoiceLockState.NOT_LOCKED.getKey());
        }
        BigDecimal total = hrBillReimbursementApplyDetails.stream().map(HrBillReimbursementApplyDetail::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        //可报销申请金额 = 原数据金额 - 已发起并且审核通过的金额
        List<String> levelIds = hrBillReimbursementApplyDetails.stream().filter(lst -> lst.getLevelId() != null).map(HrBillReimbursementApplyDetail::getLevelId).collect(Collectors.toList());
        if (levelIds != null && !levelIds.isEmpty()) {
            List<HrBillReimbursementApplyDetailDTO> levelDetailPassList = hrBillReimbursementApplyDetailRepository.getDetailInfo(levelIds, BillReimbApproveEnums.SUCCESS.getKey(), null, null);
            if (levelDetailPassList != null && !levelDetailPassList.isEmpty()) {
                BigDecimal amount = levelDetailPassList.stream().map(HrBillReimbursementApplyDetailDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                total = CalculateUtils.decimalSubtraction(total, amount);
                if (BigDecimalCompare.of(total).eq(BigDecimal.ZERO)) {
                    hrBillReimbursementApplyDTO.setIsShow(1);
                }
            }
        }
        hrBillReimbursementApplyDTO.setAmount(total);
        hrBillReimbursementApplyRepository.updateById(hrBillReimbursementApplyMapper.toEntity(hrBillReimbursementApplyDTO));
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.BILL_INVOICE.getValue(),
            flag ? BusinessTypeEnum.UPDATE.getKey() : BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrBillReimbursementApplyDTO),
            HrBillReimbursementApplyDTO.class,
            null,
            JSON.toJSONString(hrBillReimbursementApplyDTO)
        );
        return hrBillReimbursementApplyDTO;
    }

    /**
     * 修改报销申请记录以及明细
     *
     * @param hrBillReimbursementApplyDTO
     * @param applyId
     * @param checkOnDetailDtoList
     */
    private void updateHrBillReimbursementApplyDetail(HrBillReimbursementApplyDTO hrBillReimbursementApplyDTO, String applyId, List<HrBillReimbursementApplyDetailDTO> checkOnDetailDtoList) {
        JWTUserDTO curUser = SecurityUtils.getJwtUser();
        List<String> ids = checkOnDetailDtoList.stream().map(HrBillReimbursementApplyDetailDTO::getId).collect(Collectors.toList());
        List<String> levelIds = checkOnDetailDtoList.stream().map(HrBillReimbursementApplyDetailDTO::getLevelId).collect(Collectors.toList());
        ids.addAll(levelIds);
        hrBillReimbursementApplyDetailRepository.updateState(ids, BillInvoiceApproveEnums.InvoiceRecordState.AGAIN_LOCKED.getKey());
        HrBillReimbursementApply applyEntity = hrBillReimbursementApplyMapper.toEntity(hrBillReimbursementApplyDTO);
        BigDecimal totalAmount = checkOnDetailDtoList.stream().map(HrBillReimbursementApplyDetailDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        applyEntity.setId(applyId)
            .setParentId(hrBillReimbursementApplyDTO.getId())
            .setAmount(totalAmount)
            .setReimbursementState(BillInvoiceApproveEnums.InvoiceState.INVOICE_RECORD.getKey())
            .setApplyDate(LocalDate.now())
            .setLastModifiedDate(LocalDateTime.now());
        hrBillReimbursementApplyRepository.updateById(applyEntity);
        for (HrBillReimbursementApplyDetailDTO hrBillReimbursementApplyDetailDTO : checkOnDetailDtoList) {
            if (hrBillReimbursementApplyDTO.getAccountType() == null
                || (hrBillReimbursementApplyDTO.getAccountType() != null && !hrBillReimbursementApplyDTO.getAccountType().equals(BillReimbApproveEnums.InvoiceTypeEnum.SPECIAL_CLIENT.getKey()))) {
                HrBillReimbursementApplyDetail oldDetail = hrBillReimbursementApplyDetailMapper.toEntity(hrBillReimbursementApplyDetailDTO);
                oldDetail.setState(BillInvoiceApproveEnums.InvoiceRecordState.AGAIN_LOCKED.getKey());
                hrBillReimbursementApplyDetailRepository.updateById(oldDetail);
                HrBillReimbursementApplyDetail newDetail = hrBillReimbursementApplyDetailMapper.toEntity(hrBillReimbursementApplyDetailDTO);
                newDetail.setApplyId(applyEntity.getId())
                    .setId(hrBillReimbursementApplyDetailDTO.getLevelId())
                    .setState(BillInvoiceApproveEnums.InvoiceRecordState.AGAIN_LOCKED.getKey());
                hrBillReimbursementApplyDetailRepository.updateById(newDetail);
            } else {
                HrBillReimbursementApplyDetail oldDetail = new HrBillReimbursementApplyDetail();
                oldDetail.setId(hrBillReimbursementApplyDetailDTO.getId()).setState(BillInvoiceApproveEnums.InvoiceRecordState.AGAIN_LOCKED.getKey());
                hrBillReimbursementApplyDetailRepository.updateById(oldDetail);

                List<String> detailClientIds = hrBillReimbursementApplyDetailDTO.getHrBillReimbursementDetailClientDTOList().stream().map(HrBillReimbursementClientDTO::getClientBillId).collect(Collectors.toList());
                BillReimbApproveEnums.InvoiceTypeEnum enumByKey = EnumUtils.getEnumByKey(BillReimbApproveEnums.InvoiceTypeEnum.class, hrBillReimbursementApplyDetailDTO.getInvoiceType());
                switch (enumByKey) {
                    case DF_SALARY:
                        hrBillReimbursementApplyRepository.updateClientLock(BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey(), null, null, null, detailClientIds);
                        break;
                    case DF_SOCIAL_SECURITY:
                        hrBillReimbursementApplyRepository.updateClientLock(null, BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey(), null, null, detailClientIds);
                        break;
                    case DF_MEDICAL_INSURANCE:
                        hrBillReimbursementApplyRepository.updateClientLock(null, null, BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey(), null, detailClientIds);
                        break;
                    case DF_ACCUMULATION_FOUND:
                        hrBillReimbursementApplyRepository.updateClientLock(null, null, null, BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey(), detailClientIds);
                        break;
                    default:
                        break;
                }
                //TODO 特殊客户重新发起
                HrBillReimbursementApplyDetail newDetail = hrBillReimbursementApplyDetailMapper.toEntity(hrBillReimbursementApplyDetailDTO);
                newDetail.setApplyId(applyEntity.getId())
                    .setId(hrBillReimbursementApplyDetailDTO.getLevelId())
                    .setState(BillInvoiceApproveEnums.InvoiceRecordState.AGAIN_LOCKED.getKey());
                hrBillReimbursementApplyDetailRepository.updateById(newDetail);
            }

        }
        this.saveApplyOpLogs(applyEntity.getId(), applyEntity.getApplyUserId(), curUser.getId(), "重新发起申请", "", false);
    }

    /**
     * 添加报销申请记录以及明细
     *
     * @param hrBillReimbursementApplyDTO
     * @param detailDTOList
     * @param flag
     */
    private void saveBillReimbursementApplyDetail(HrBillReimbursementApplyDTO hrBillReimbursementApplyDTO, List<HrBillReimbursementApplyDetailDTO> detailDTOList, Boolean flag) {
        JWTUserDTO curUser = SecurityUtils.getJwtUser();
        HrBillReimbursementApplyDTO reimbursementApplyDTO = new HrBillReimbursementApplyDTO();
        BeanUtils.copyProperties(hrBillReimbursementApplyDTO, reimbursementApplyDTO);
        if (hrBillReimbursementApplyDTO.getFlag()) {
            reimbursementApplyDTO.setApplyUserId(curUser.getId());
        }
        BigDecimal totalAmount = detailDTOList.stream().map(HrBillReimbursementApplyDetailDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        String parentId = hrBillReimbursementApplyDTO.getId();
        if (flag) {
            parentId = hrBillReimbursementApplyDTO.getParentId() == null ? hrBillReimbursementApplyDTO.getId() : hrBillReimbursementApplyDTO.getParentId();
        }
        reimbursementApplyDTO.setId(RandomUtil.generateId())
            .setParentId(parentId)
            .setApplyDate(LocalDate.now())
            .setReimbursementState(BillInvoiceApproveEnums.InvoiceState.INVOICE_RECORD.getKey())
            .setAmount(totalAmount)
            .setApplyDate(LocalDate.now())
            .setCreatedDate(LocalDateTime.now());
        reimbursementApplyDTO.setLastModifiedDate(LocalDateTime.now());
        HrBillReimbursementApply hrBillReimbursementApply = hrBillReimbursementApplyMapper.toEntity(reimbursementApplyDTO);
        // 保存附件列表
        if (hrBillReimbursementApplyDTO.getEnclosures() != null) {
            hrBillReimbursementApplyDTO.getEnclosures().forEach(e -> {
                this.hrAppendixService.saveAppendixUnion(e.getId(), hrBillReimbursementApply.getId());
            });
        }
        List<String> clientIds = new ArrayList<>();
        //特殊客户处理
        if (hrBillReimbursementApplyDTO.getAccountType() != null && hrBillReimbursementApplyDTO.getAccountType().equals(BillReimbApproveEnums.InvoiceTypeEnum.SPECIAL_CLIENT.getKey())) {
            List<HrBillReimbursementClientDTO> dtoList = hrBillReimbursementApplyDTO.getHrBillReimbursementClientDTOList();
            for (HrBillReimbursementApplyDetailDTO hrBillReimbursementApplyDetailDTO : detailDTOList) {
                List<HrBillReimbursementClientDTO> reimbursementClientDTOList = hrBillReimbursementApplyDetailDTO.getHrBillReimbursementDetailClientDTOList().stream().filter(lst -> lst.getCheckOn() != null && lst.getCheckOn() == 1).collect(Collectors.toList());
                HrBillReimbursementApplyDetail oldDetail = new HrBillReimbursementApplyDetail();
                oldDetail.setId(hrBillReimbursementApplyDetailDTO.getId()).setLastModifiedDate(LocalDateTime.now());

                HrBillReimbursementApplyDetail newDetail = hrBillReimbursementApplyDetailMapper.toEntity(hrBillReimbursementApplyDetailDTO);
                newDetail.setId(RandomUtil.generateId())
                    .setParentId(oldDetail.getId())
                    .setApplyId(hrBillReimbursementApply.getId())
                    .setState(BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey())
                    .setCreatedDate(LocalDateTime.now());
                hrBillReimbursementApplyDetailRepository.insert(newDetail);

                List<String> ids = new ArrayList<>();
                //处理客户账单每一报销项锁定状态
                this.handleSpecialApplyDetail(oldDetail, hrBillReimbursementApplyDetailDTO, reimbursementClientDTOList, dtoList, ids, clientIds);
                ids = ids.stream().distinct().collect(Collectors.toList());
                //查找之前发起的审核拒绝的报销记录，更改锁定状态为重新锁定
                List<HrBillReimbursementApplyDetailDTO> detailInfoList = hrBillReimbursementApplyDetailRepository.getDetailInfo(null, BillReimbApproveEnums.REJECT.getKey(), oldDetail.getId(), hrBillReimbursementApplyDetailDTO.getInvoiceType());
                if (detailInfoList != null && !detailInfoList.isEmpty()) {
                    List<String> detailIds = detailInfoList.stream().map(HrBillReimbursementApplyDetailDTO::getId).collect(Collectors.toList());
                    List<HrBillReimbursementClientDTO> list = hrBillReimbursementApplyDetailRepository.getDetailClientById(detailIds, ids);
                    if (list != null && !list.isEmpty()) {
                        List<String> collect = list.stream().map(HrBillReimbursementClientDTO::getDetailId).collect(Collectors.toList());
                        hrBillReimbursementApplyDetailRepository.updateState(collect, BillInvoiceApproveEnums.InvoiceRecordState.AGAIN_LOCKED.getKey());
                    }
                }
                if (CollectionUtils.isNotEmpty(ids)) {
                    for (String clientBillId : ids) {
                        HrBillReimbursementClientDTO reimbursementClient = new HrBillReimbursementClientDTO();
                        reimbursementClient.setId(RandomUtil.generateId())
                            .setDetailId(newDetail.getId())
                            .setClientBillId(clientBillId)
                            .setCreatedBy(curUser.getUserName())
                            .setCreatedDate(LocalDateTime.now());
                        hrBillReimbursementApplyDetailRepository.insertDetailClient(reimbursementClient);
                    }
                }
                hrBillReimbursementApplyDetailRepository.updateById(oldDetail);
            }
        } else {
            for (HrBillReimbursementApplyDetailDTO hrBillReimbursementApplyDetailDTO : detailDTOList) {
                HrBillReimbursementApplyDetail oldDetail = hrBillReimbursementApplyDetailMapper.toEntity(hrBillReimbursementApplyDetailDTO);
                HrBillReimbursementApplyDetail newDetail = hrBillReimbursementApplyDetailMapper.toEntity(hrBillReimbursementApplyDetailDTO);
                newDetail.setId(RandomUtil.generateId())
                    .setApplyId(hrBillReimbursementApply.getId())
                    .setState(BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey())
                    .setCreatedDate(LocalDateTime.now());
                hrBillReimbursementApplyDetailRepository.insert(newDetail);
                //原报销明细绑定新报销明细Id，并且锁定
                oldDetail.setLevelId(newDetail.getId())
                    .setState(BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey())
                    .setLastModifiedDate(LocalDateTime.now());
                hrBillReimbursementApplyDetailRepository.updateById(oldDetail);
                if (flag && hrBillReimbursementApplyDTO.getParentId() != null) {//处理审核未通过有上级报销的明细
                    HrBillReimbursementApplyDetail applyDetail = hrBillReimbursementApplyDetailRepository.selectOne(new QueryWrapper<HrBillReimbursementApplyDetail>()
                        .eq("apply_id", hrBillReimbursementApplyDTO.getParentId()).eq("level_id", oldDetail)
                        .orderByDesc("created_date").last("LIMIT 1"));
                    if (applyDetail != null) {
                        applyDetail.setLevelId(newDetail.getId())
                            .setInvoiceType(oldDetail.getInvoiceType())
                            .setContent(oldDetail.getContent())
                            .setAmount(oldDetail.getAmount())
                            .setState(BillInvoiceApproveEnums.InvoiceRecordState.AGAIN_LOCKED.getKey())
                            .setRemark(oldDetail.getRemark())
                            .setLastModifiedDate(LocalDateTime.now());
                        hrBillReimbursementApplyDetailRepository.updateById(applyDetail);
                    }
                }
            }


        }
        String clientId = hrBillReimbursementApplyDTO.getClientId();
        HrClient rootParentClient = clientId == null ? null : hrClientRepository.getRootParentClient(clientId);
        if (rootParentClient != null && SpecialBillClient.HAIER.getKey().equals(rootParentClient.getId()) && hrBillReimbursementApplyDTO.getAccountType() != null
            && hrBillReimbursementApplyDTO.getAccountType().equals(BillReimbApproveEnums.InvoiceTypeEnum.SPECIAL_CLIENT.getKey())) {
            List<String> collect = clientIds.stream().distinct().collect(Collectors.toList());
            if (collect.size() == 1) {
                String valClientId = clientIds.get(0);
                HrClient hrClient = hrClientRepository.selectById(valClientId);
                String title = reimbursementApplyDTO.getTitle().replace(reimbursementApplyDTO.getClientName(), hrClient.getClientName());
                hrBillReimbursementApply.setClientId(valClientId);
                hrBillReimbursementApply.setTitle(title);
            }
        }
        hrBillReimbursementApplyRepository.insert(hrBillReimbursementApply);
        // 对账产生的,更新报销客户表与报销申请的关联关系
        if (reimbursementApplyDTO.getBillCompareResultId() != null && reimbursementApplyDTO.getHrBillReimbursementClientDTOList() != null && !reimbursementApplyDTO.getHrBillReimbursementClientDTOS().isEmpty()) {
            reimbursementApplyDTO.getHrBillReimbursementClientDTOS().forEach(e -> {
                e.setApplyId(hrBillReimbursementApply.getId());
            });
            hrBillReimbursementClientService.updateBatchById(hrBillReimbursementClientMapper.toEntity(reimbursementApplyDTO.getHrBillReimbursementClientDTOS()));
        }
        // 如果发起了申请则保存日志
        if (BillReimbApproveEnums.ON_CUSTOMER_MANAGER.getKey().equals(hrBillReimbursementApply.getApproveStatus())) {
            this.saveApplyOpLogs(hrBillReimbursementApply.getId(), hrBillReimbursementApply.getApplyUserId(), curUser.getId(),
                "发起申请", "", false);
        }
    }

    /**
     * 处理特殊客户每一项锁定状态
     *
     * @param oldDetail                         原数据/可申请报销(只有ID)
     * @param hrBillReimbursementApplyDetailDTO 原数据/可申请报销(全数据)
     * @param reimbursementClientDTOList        明细金额详情
     * @param dtoList                           全部明细详情
     * @param idList                            明细详情ID
     * @param clientIds                         客户ID
     */
    private void handleSpecialApplyDetail(HrBillReimbursementApplyDetail oldDetail,
                                          HrBillReimbursementApplyDetailDTO hrBillReimbursementApplyDetailDTO,
                                          List<HrBillReimbursementClientDTO> reimbursementClientDTOList,
                                          List<HrBillReimbursementClientDTO> dtoList,
                                          List<String> idList,
                                          List<String> clientIds) {
        BillReimbApproveEnums.InvoiceTypeEnum enumByKey = EnumUtils.getEnumByKey(BillReimbApproveEnums.InvoiceTypeEnum.class, hrBillReimbursementApplyDetailDTO.getInvoiceType());
        switch (enumByKey) {
            case DF_SALARY:
                List<HrBillReimbursementClientDTO> collect = reimbursementClientDTOList.stream().filter(lst -> lst.getRealSalaryLock().equals(BillInvoiceApproveEnums.InvoiceRecordState.NOT_LOCKED.getKey())
                    || lst.getRealSalaryLock().equals(BillInvoiceApproveEnums.InvoiceRecordState.UNLOCK.getKey())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    List<String> ids = collect.stream().map(HrBillReimbursementClientDTO::getId).distinct().collect(Collectors.toList());
                    List<String> clientIdList = collect.stream().map(HrBillReimbursementClientDTO::getClientId).distinct().collect(Collectors.toList());
                    idList.addAll(ids);
                    clientIds.addAll(clientIdList);
                    hrBillReimbursementApplyRepository.updateClientLock(BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey(), null, null, null, ids);
                    boolean bool = true;
                    for (HrBillReimbursementClientDTO hrBillReimbursementClientDTO : dtoList) {
                        HrBillReimbursementClientDTO dto = collect.stream().filter(ls -> ls.getId().equals(hrBillReimbursementClientDTO.getId())).findAny().orElse(null);
                        if (dto != null) {
                            hrBillReimbursementClientDTO.setRealSalaryLock(BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey());
                        }
                        if (hrBillReimbursementClientDTO.getRealSalaryLock().equals(BillInvoiceApproveEnums.InvoiceRecordState.NOT_LOCKED.getKey())
                            || hrBillReimbursementClientDTO.getRealSalaryLock().equals(BillInvoiceApproveEnums.InvoiceRecordState.UNLOCK.getKey())) {
                            bool = false;
                        }
                    }
                    if (bool) {
                        oldDetail.setState(BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey());
                    }
                }
                break;
            case DF_SOCIAL_SECURITY:
                List<HrBillReimbursementClientDTO> collect1 = reimbursementClientDTOList.stream().filter(lst -> lst.getSocialSecurityLock().equals(BillInvoiceApproveEnums.InvoiceRecordState.NOT_LOCKED.getKey())
                    || lst.getSocialSecurityLock().equals(BillInvoiceApproveEnums.InvoiceRecordState.UNLOCK.getKey())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect1)) {
                    List<String> ids = collect1.stream().map(HrBillReimbursementClientDTO::getId).distinct().collect(Collectors.toList());
                    List<String> clientIdList = collect1.stream().map(HrBillReimbursementClientDTO::getClientId).distinct().collect(Collectors.toList());
                    idList.addAll(ids);
                    clientIds.addAll(clientIdList);
                    hrBillReimbursementApplyRepository.updateClientLock(null, BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey(), null, null, ids);
                    boolean bool = true;
                    for (HrBillReimbursementClientDTO lst : dtoList) {
                        HrBillReimbursementClientDTO dto = collect1.stream().filter(ls -> ls.getId().equals(lst.getId())).findAny().orElse(null);
                        if (dto != null) {
                            lst.setSocialSecurityLock(BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey());
                        }
                        if (lst.getSocialSecurityLock().equals(BillInvoiceApproveEnums.InvoiceRecordState.NOT_LOCKED.getKey())
                            || lst.getSocialSecurityLock().equals(BillInvoiceApproveEnums.InvoiceRecordState.UNLOCK.getKey())) {
                            bool = false;
                        }
                    }
                    if (bool) {
                        oldDetail.setState(BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey());
                    }
                }
                break;
            case DF_MEDICAL_INSURANCE:
                List<HrBillReimbursementClientDTO> collect2 = reimbursementClientDTOList.stream().filter(lst -> lst.getMedicalInsuranceLock().equals(BillInvoiceApproveEnums.InvoiceRecordState.NOT_LOCKED.getKey())
                    || lst.getMedicalInsuranceLock().equals(BillInvoiceApproveEnums.InvoiceRecordState.UNLOCK.getKey())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect2)) {
                    List<String> ids = collect2.stream().map(HrBillReimbursementClientDTO::getId).distinct().collect(Collectors.toList());
                    List<String> clientIdList = collect2.stream().map(HrBillReimbursementClientDTO::getClientId).distinct().collect(Collectors.toList());
                    idList.addAll(ids);
                    clientIds.addAll(clientIdList);
                    hrBillReimbursementApplyRepository.updateClientLock(null, null, BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey(), null, ids);
                    boolean bool = true;
                    for (HrBillReimbursementClientDTO lst : dtoList) {
                        HrBillReimbursementClientDTO dto = collect2.stream().filter(ls -> ls.getId().equals(lst.getId())).findAny().orElse(null);
                        if (dto != null) {
                            lst.setMedicalInsuranceLock(BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey());
                        }
                        if (lst.getMedicalInsuranceLock().equals(BillInvoiceApproveEnums.InvoiceRecordState.NOT_LOCKED.getKey())
                            || lst.getMedicalInsuranceLock().equals(BillInvoiceApproveEnums.InvoiceRecordState.UNLOCK.getKey())) {
                            bool = false;
                        }
                    }
                    if (bool) {
                        oldDetail.setState(BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey());
                    }
                }
                break;
            case DF_ACCUMULATION_FOUND:
                List<HrBillReimbursementClientDTO> collect3 = reimbursementClientDTOList.stream().filter(lst -> lst.getAccumulationFoundLock().equals(BillInvoiceApproveEnums.InvoiceRecordState.NOT_LOCKED.getKey())
                    || lst.getAccumulationFoundLock().equals(BillInvoiceApproveEnums.InvoiceRecordState.UNLOCK.getKey())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect3)) {
                    List<String> ids = collect3.stream().map(HrBillReimbursementClientDTO::getId).distinct().collect(Collectors.toList());
                    List<String> clientIdList = collect3.stream().map(HrBillReimbursementClientDTO::getClientId).distinct().collect(Collectors.toList());
                    idList.addAll(ids);
                    clientIds.addAll(clientIdList);
                    hrBillReimbursementApplyRepository.updateClientLock(null, null, null, BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey(), ids);
                    boolean bool = true;
                    for (HrBillReimbursementClientDTO lst : dtoList) {
                        HrBillReimbursementClientDTO dto = collect3.stream().filter(ls -> ls.getId().equals(lst.getId())).findAny().orElse(null);
                        if (dto != null) {
                            lst.setAccumulationFoundLock(BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey());
                        }
                        if (lst.getAccumulationFoundLock().equals(BillInvoiceApproveEnums.InvoiceRecordState.NOT_LOCKED.getKey())
                            || lst.getAccumulationFoundLock().equals(BillInvoiceApproveEnums.InvoiceRecordState.UNLOCK.getKey())) {
                            bool = false;
                        }
                    }
                    if (bool) {
                        oldDetail.setState(BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey());
                    }
                }
                break;
            default:
                oldDetail.setState(BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey());
                break;
        }
    }

    /**
     * 保存费用项明细和附件明细
     *
     * @param applyId
     * @param detailDTOList
     * @param enclosures
     */
    private void saveFeeDetailAndEnclosures(String applyId, List<HrBillReimbursementApplyDetailDTO> detailDTOList, List<HrAppendixDTO> enclosures) {
        // 保存费用项明细
        detailDTOList.forEach(detail -> detail.setApplyId(applyId));
        this.hrBillReimbursementApplyDetailService.saveBatch(this.hrBillReimbursementApplyDetailMapper.toEntity(detailDTOList));

        // 保存附件列表
        if (enclosures != null) {
            enclosures.forEach(e -> {
                this.hrAppendixService.saveAppendixUnion(e.getId(), applyId);
            });
        }
    }


    /**
     * 修改报销申请
     *
     * @param hrBillReimbursementApplyDTO
     * @return
     */
    @Override
    public Optional<HrBillReimbursementApplyDTO> updateHrBillReimbursementApply(HrBillReimbursementApplyDTO hrBillReimbursementApplyDTO) {
        return Optional.ofNullable(this.hrBillReimbursementApplyRepository.selectById(hrBillReimbursementApplyDTO.getId()))
            .map(apply -> {
                // 保存账单明细
                List<HrBillReimbursementApplyDetailDTO> detailDTOList = hrBillReimbursementApplyDTO.getDetailDTOList();
                if (detailDTOList == null || detailDTOList.size() == 0) {
                    throw new CommonException("账单明细不能为空！");
                }
                // 只能修改未发起的申请
                if (apply.getReimbursementState().equals(BillInvoiceApproveEnums.InvoiceState.INVOICE_RECORD.getKey())
                    && !BillReimbApproveEnums.NOT_LAUNCH.getKey().equals(apply.getApproveStatus())
                    && !BillReimbApproveEnums.REJECT.getKey().equals(apply.getApproveStatus())) {
                    throw new CommonException("只能修改[未发起和审批拒绝]状态的申请!");
                }
                if (BillReimbApproveEnums.REJECT.getKey().equals(apply.getApproveStatus())) {
                    List<String> collect = detailDTOList.stream().filter(lst -> lst.getState().equals(BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey())
                            || lst.getState().equals(BillInvoiceApproveEnums.InvoiceRecordState.AGAIN_LOCKED.getKey()))
                        .map(HrBillReimbursementApplyDetailDTO::getInvoiceTypeName).collect(Collectors.toList());
                    if (hrBillReimbursementApplyDTO.getApproveStatus().equals(BillReimbApproveEnums.NOT_LAUNCH.getKey())) {
                        //审核未通过基础上暂存状态不改变
                        hrBillReimbursementApplyDTO.setApproveStatus(apply.getApproveStatus());
                    }
                    if (CollectionUtils.isNotEmpty(collect)) {
                        throw new CommonException("报销明细中[ " + String.join(",", collect) + " ]已重新发起报销申请，不可再次发起！");
                    }
                    /*List<String> levelIds = detailDTOList.stream().map(HrBillReimbursementApplyDetailDTO::getId).collect(Collectors.toList());
                    if (apply.getParentId() != null){
                        this.selectApplyDetail(apply.getParentId(),levelIds);
                    }
                    hrBillReimbursementApplyDetailRepository.updateState(levelIds, BillInvoiceApproveEnums.InvoiceRecordState.AGAIN_LOCKED.getKey());*/
                    if (apply.getParentId() != null) {
                        List<HrBillReimbursementClientDTO> dtoList = hrBillReimbursementApplyDTO.getHrBillReimbursementClientDTOList();
                        for (HrBillReimbursementApplyDetailDTO detailDTO : detailDTOList) {
                            //如果是特殊客户重新锁定
                            if (apply.getAccountType() != null && apply.getAccountType().equals(BillReimbApproveEnums.InvoiceTypeEnum.SPECIAL_CLIENT.getKey())) {
                                HrBillReimbursementApplyDetail oldDetail = hrBillReimbursementApplyDetailRepository.selectOne(new QueryWrapper<HrBillReimbursementApplyDetail>()
                                    .eq("apply_id", apply.getParentId()).eq("invoice_type", detailDTO.getInvoiceType()).orderByDesc("created_date").last("LIMIT 1"));
                                this.handleSpecialApplyDetail(oldDetail, detailDTO, detailDTO.getHrBillReimbursementDetailClientDTOList(), dtoList, new ArrayList<>(), new ArrayList<>());
                                //特殊客户解除明细账单对应锁定状态
                                List<String> ids = new ArrayList<>();
                                ids.add(detailDTO.getId());
                                if (oldDetail.getState().equals(BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey())
                                    || oldDetail.getState().equals(BillInvoiceApproveEnums.InvoiceRecordState.AGAIN_LOCKED.getKey())) {
                                    ids.add(oldDetail.getId());
                                }
                                hrBillReimbursementApplyDetailRepository.updateState(ids, BillInvoiceApproveEnums.InvoiceRecordState.AGAIN_LOCKED.getKey());
                            } else {
                                HrBillReimbursementApplyDetail applyDetail = hrBillReimbursementApplyDetailRepository.selectOne(new QueryWrapper<HrBillReimbursementApplyDetail>()
                                    .eq("apply_id", apply.getParentId()).eq("level_id", detailDTO.getId()).orderByDesc("created_date").last("LIMIT 1"));
                                List<String> ids = Arrays.asList(detailDTO.getId(), applyDetail.getId());
                                hrBillReimbursementApplyDetailRepository.updateState(ids, BillInvoiceApproveEnums.InvoiceRecordState.AGAIN_LOCKED.getKey());
                            }
                        }

                        QueryWrapper<HrBillReimbursementApplyDetail> qw = new QueryWrapper<>();
                        qw.eq("apply_id", apply.getParentId());
                        qw.in("state", BillInvoiceApproveEnums.InvoiceRecordState.NOT_LOCKED.getKey(), BillInvoiceApproveEnums.InvoiceRecordState.UNLOCK.getKey());
                        List<HrBillReimbursementApplyDetail> detailList = hrBillReimbursementApplyDetailRepository.selectList(qw);
                        Integer lockState;
                        if (CollectionUtils.isEmpty(detailList)) {
                            lockState = BillInvoiceApproveEnums.InvoiceLockState.LOCKED.getKey();
                        } else {
                            lockState = BillInvoiceApproveEnums.InvoiceLockState.NOT_LOCKED.getKey();
                        }
                        hrBillReimbursementApplyRepository.updateLockState(apply.getParentId(), lockState);
                    }
                }
                BigDecimal amount = detailDTOList.stream().map(HrBillReimbursementApplyDetailDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                hrBillReimbursementApplyDTO.setAmount(amount);
                HrBillReimbursementApply hrBillReimbursementApply = this.hrBillReimbursementApplyMapper.toEntity(hrBillReimbursementApplyDTO);
                this.hrBillReimbursementApplyRepository.updateById(hrBillReimbursementApply);
                log.info("Update HrBillReimbursementApply:{}", hrBillReimbursementApplyDTO);

                // 删除附件
                this.hrAppendixService.deleteByUnionId(apply.getId());
                // 保存附件列表
                if (hrBillReimbursementApplyDTO.getEnclosures() != null) {
                    hrBillReimbursementApplyDTO.getEnclosures().forEach(e -> {
                        this.hrAppendixService.saveAppendixUnion(e.getId(), apply.getId());
                    });
                }
                for (HrBillReimbursementApplyDetailDTO hrBillReimbursementApplyDetailDTO : detailDTOList) {
                    HrBillReimbursementApplyDetail applyDetail = hrBillReimbursementApplyDetailMapper.toEntity(hrBillReimbursementApplyDetailDTO);
                    hrBillReimbursementApplyDetailRepository.updateById(applyDetail);
                }
                // 未发起、审批拒绝改为发起
                if ((BillReimbApproveEnums.NOT_LAUNCH.getKey().equals(apply.getApproveStatus()) || BillReimbApproveEnums.REJECT.getKey().equals(apply.getApproveStatus())) &&
                    BillInvoiceApproveEnums.ON_CUSTOMER_MANAGER.getKey().equals(hrBillReimbursementApplyDTO.getApproveStatus())) {
                    JWTUserDTO curUser = SecurityUtils.getJwtUser();
                    // 保存日志
                    this.saveApplyOpLogs(apply.getId(), apply.getApplyUserId(), curUser.getId(), BillReimbApproveEnums.NOT_LAUNCH.getKey().equals(apply.getApproveStatus()) ? "发起申请" : "重新发起申请", "", false);
                }
                return hrBillReimbursementApplyDTO;
            });
    }

    /**
     * 查询父级明细
     *
     * @param parentId
     * @param detailIdList
     */
    private void selectApplyDetail(String parentId, List<String> detailIdList) {
        QueryWrapper<HrBillReimbursementApplyDetail> qw = new QueryWrapper<>();
        qw.eq("apply_id", parentId);
        qw.in("level_id", detailIdList);
        List<HrBillReimbursementApplyDetail> hrBillReimbursementApplyDetails = hrBillReimbursementApplyDetailRepository.selectList(qw);
        if (CollectionUtils.isNotEmpty(hrBillReimbursementApplyDetails)) {
            List<String> ids = hrBillReimbursementApplyDetails.stream().map(HrBillReimbursementApplyDetail::getId).collect(Collectors.toList());
            detailIdList.addAll(ids);
        }
    }

    /**
     * 查询报销申请详情
     *
     * @param id
     * @return
     */
    @Override
    public HrBillReimbursementApplyDTO getHrBillReimbursementApply(String id) {
        log.info("Get HrBillReimbursementApply :{}", id);

        HrBillReimbursementApplyDTO applyDTO = this.hrBillReimbursementApplyRepository.getById(id);

        // 获取要通知的角色名称列表
        String noticeRoles = applyDTO.getNoticeRoles();
        if (StringUtils.isNotEmpty(noticeRoles)) {
            String roleNames = this.roleRepository.getRolesByIds(Arrays.asList(noticeRoles.split(",")));
            applyDTO.setNoticeRolesName(roleNames);
        }

        // 获取费用明细
        List<HrBillReimbursementApplyDetailDTO> detailDTOS = this.findApplyDetailByApplyId(applyDTO.getId(), applyDTO);
        applyDTO.setDetailDTOList(detailDTOS);

        // 获取附件列表
        List<HrAppendixDTO> appendixDTOS = this.hrAppendixService.getByUnionId(id);
        applyDTO.setEnclosures(appendixDTOS);

        // 获取审核详情
        List<HrApplyOpLogsDTO> applyOpLogsDTOS = this.hrApplyOpLogsService.getByApplyId(id);
        applyDTO.setApplyOpLogsDTOS(applyOpLogsDTOS);

        return applyDTO;
    }

    private List<HrBillReimbursementApplyDetailDTO> findApplyDetailByApplyId(String id, HrBillReimbursementApplyDTO applyDTO) {
        List<HrBillReimbursementApplyDetailDTO> detailDTOS = this.hrBillReimbursementApplyDetailRepository.getByApplyId(id);
        if (applyDTO.getAccountType() != null) {
            if (applyDTO.getAccountType().equals(BillReimbApproveEnums.InvoiceTypeEnum.DF_SALARY.getKey())) {
                List<HrBillReimbursementClientDTO> hrBillReimbursementClientDTOList = hrBillReimbursementApplyRepository.findBillByApplyId(id);
                applyDTO.setHrBillReimbursementClientDTOList(hrBillReimbursementClientDTOList);
            } else if (applyDTO.getAccountType().equals(BillReimbApproveEnums.InvoiceTypeEnum.SPECIAL_CLIENT.getKey())) {
                List<HrBillReimbursementClientDTO> hrBillReimbursementClientDTOList = hrBillReimbursementApplyRepository.findSpecialClientInfo(id);
                if (hrBillReimbursementClientDTOList == null || hrBillReimbursementClientDTOList.isEmpty() && applyDTO.getParentId() != null) {
                    List<HrBillReimbursementClientDTO> hrBillReimbursementClientDTO = hrBillReimbursementApplyRepository.findSpecialClientInfo(applyDTO.getParentId());
                    applyDTO.setHrBillReimbursementClientDTOList(hrBillReimbursementClientDTO);
                } else {
                    applyDTO.setHrBillReimbursementClientDTOList(hrBillReimbursementClientDTOList);
                }
                for (HrBillReimbursementApplyDetailDTO detailDTO : detailDTOS) {
                    List<HrBillReimbursementClientDTO> hrBillReimbursementDetailClientDTOList = hrBillReimbursementApplyDetailRepository.getDetailClientById(Collections.singletonList(detailDTO.getId()), null);
                    detailDTO.setHrBillReimbursementDetailClientDTOList(hrBillReimbursementDetailClientDTOList);
                }
            } else {
                List<HrBillReimbursementClientDTO> hrBillReimbursementClientDTOList = hrBillReimbursementApplyRepository.findAccountByApplyId(id);
                applyDTO.setHrBillReimbursementClientDTOList(hrBillReimbursementClientDTOList);
                List<HrBillReimbursementClientDTO> reimbursementClientDTOList = hrBillReimbursementApplyDetailRepository.findListByApplyId(Collections.singletonList(id));
                applyDTO.setHrBillReimbursementClientDTOS(reimbursementClientDTOList);
            }
        }
        return detailDTOS;
    }

    /**
     * 删除报销申请
     *
     * @param id
     */
    @Override
    public void deleteHrBillReimbursementApply(String id) {
        Optional.ofNullable(this.hrBillReimbursementApplyRepository.selectById(id))
            .ifPresent(hrBillReimbursementApply -> {
                //可开发票数据是流程自动化数据不可删除
                if (hrBillReimbursementApply.getReimbursementState().equals(BillInvoiceApproveEnums.InvoiceState.OPENABLE_INVOICE.getKey())) {
                    throw new CommonException("不可删除流程自动化报销数据！");
                }
                if (hrBillReimbursementApply.getAccountType() == null
                    && !hrBillReimbursementApply.getApproveStatus().equals(BillReimbApproveEnums.REJECT.getKey())
                    && !hrBillReimbursementApply.getApproveStatus().equals(BillReimbApproveEnums.ALREADY_CANCEL.getKey())
                ) {
                    throw new CommonException("只能删除[审核拒绝、已作废]的申请!");
                }
                // 只能删除审核拒绝申请的
                if (hrBillReimbursementApply.getAccountType() != null
                    && !hrBillReimbursementApply.getApproveStatus().equals(BillReimbApproveEnums.NOT_LAUNCH.getKey())
                    && !hrBillReimbursementApply.getApproveStatus().equals(BillReimbApproveEnums.REJECT.getKey())
                    && !hrBillReimbursementApply.getApproveStatus().equals(BillReimbApproveEnums.ALREADY_CANCEL.getKey())
                ) {
                    throw new CommonException("只能删除[审核拒绝、已作废]的申请!");
                }
                List<HrBillReimbursementApplyDetail> detailList = hrBillReimbursementApplyDetailRepository.selectList(new QueryWrapper<HrBillReimbursementApplyDetail>().eq("apply_id", id));
                if (detailList != null && !detailList.isEmpty()) {
                    List<String> detailIds = detailList.stream().map(HrBillReimbursementApplyDetail::getId).collect(Collectors.toList());
                    hrBillReimbursementApplyDetailRepository.deleteBatchIds(detailIds);
                    hrBillReimbursementApplyDetailRepository.deleteDetailClient(detailIds);
                }
                this.hrBillReimbursementApplyRepository.deleteById(id);
                hrBillReimbursementApplyRepository.delApplyClientId(id);
                log.info("Delete HrBillReimbursementApply:{}", hrBillReimbursementApply);
            });
    }

    /**
     * 批量删除报销申请
     *
     * @param ids
     */
    @Override
    public void deleteHrBillReimbursementApply(List<String> ids) {
        log.info("Delete HrBillReimbursementApplys:{}", ids);
        if (ids == null || ids.isEmpty()) {
            return;
        }
        ids.forEach(this::deleteHrBillReimbursementApply);
        this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.BILL_INVOICE.getValue(), BusinessTypeEnum.DELETE.getKey(), JSON.toJSONString(ids), null, null, null, null, null, null);
    }

    /**
     * 分页查询报销申请
     *
     * @param param
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrBillReimbursementApplyDTO param, Long pageNumber, Long pageSize) {
        Page<HrBillReimbursementApply> page = new Page<>(pageNumber, pageSize);
        page.setOrders(param.createOrderItems());

        JWTUserDTO curUser = SecurityUtils.getJwtUser();
        param.setCurUserId(curUser.getId());
        log.info("当前登录用户===================>:{}", curUser);

        BillReimbApproveEnums approveEnums = EnumUtils.getEnum(BillReimbApproveEnums.class, "getRoleKey", curUser.getCurrentRoleKey());
        if (approveEnums != null) {
            param.setCurUserStartStatus(approveEnums.getKey());
        }

        // 获取当前用户查看的客户列表
        // List<String> clientIds = hrClientService.selectClientIdByUserId();
        // param.setAllowClientIds(clientIds);

        IPage iPage = this.hrBillReimbursementApplyRepository.queryForPage(page, param);
        return iPage;
    }

    /**
     * 审核申请
     *
     * @param param
     */
    @Override
    public void auditApply(HrBillReimbursementApplyDTO param) {
        Integer type = param.getAuditType();

        // 拒绝时输入拒绝原因
        if (AUDIT_REJECT.equals(type) && StringUtils.isEmpty(param.getRejectMsg())) {
            throw new CommonException("请输入拒绝原因!");
        }
        List<HrBillReimbursementApplyDTO> hrBillReimbursementApplyDTOS = hrBillReimbursementApplyRepository.selectByIdList(param.getIds());
        for (HrBillReimbursementApplyDTO applyDTO : hrBillReimbursementApplyDTOS) {
            applyDTO.setAuditType(type).setRejectMsg(param.getRejectMsg());

            // 获取当前用户
            JWTUserDTO userDTO = SecurityUtils.getJwtUser();
            log.info("当前用户===========================>:{}", userDTO);

            BillReimbApproveEnums approveEnums = EnumUtils.getEnumByKey(BillReimbApproveEnums.class, applyDTO.getApproveStatus());

            // 只有会计可以确认
            if (!AUDIT_CONFIRM.equals(type) && UserRoleTypeEnum.ACCOUNTING.getKey().equals(userDTO.getCurrentRoleKey())) {
                throw new CommonException("会计角色可以确认申请!");
            }

            switch (approveEnums) {
                case NOT_LAUNCH:
                    throw new CommonException("申请未发起!");
                case SUCCESS:
                case REJECT:
                    throw new CommonException("审核流程已结束,操作异常!");
                case ON_CUSTOMER_MANAGER:
                    // 待客服经理审批
                    this.auditByCustomerManager(userDTO, approveEnums, applyDTO);
                    break;
                case ON_ACCOUNTING:
                    // 待会计确认
                    this.confirmByAccounting(userDTO, approveEnums, applyDTO);
                    break;
                default:
                    break;
            }
            // 更新申请
            this.updateById(this.hrBillReimbursementApplyMapper.toEntity(applyDTO));
            hrBillReimbursementApplyDetailService.handleParentInvoiceData(applyDTO, type);
        }
    }

    /**
     * 获取可催办的审批人
     *
     * @param applyId 申请id
     * @return
     */
    @Override
    public List<UserDTO> getUrgeUsers(String applyId) {
        HrBillReimbursementApplyDTO applyDTO = this.hrBillReimbursementApplyRepository.getById(applyId);

        // 获取当前审批流程对应的角色
        BillReimbApproveEnums approveEnums = EnumUtils.getEnumByKey(BillReimbApproveEnums.class, applyDTO.getApproveStatus());
        String roleKey = approveEnums.getRoleKey();
        if (StringUtils.isEmpty(roleKey)) {
            return null;
        }

        // 获取当前rolekey对应的用户列表
        return this.userRepository.getByRoles(Collections.singletonList(roleKey));
    }

    /**
     * 报销申请催办
     *
     * @param param
     */
    @Override
    public void reimbursementApplyUrge(BillReimApplyUrgeDTO param) {
        if (StringUtils.isEmpty(param.getUrgeUserId())) {
            throw new CommonException("被催办的用户不能为空!");
        }
        if (StringUtils.isEmpty(param.getContent())) {
            throw new CommonException("催办内容不能为空!");
        }
        String message = "";
        // 获取申请信息
        HrBillReimbursementApplyDTO applyDTO = this.hrBillReimbursementApplyRepository.getById(param.getApplyId());
        JWTUserDTO userDTO = SecurityUtils.getJwtUser();
        if (param.isSendWxMsg()) {
            // TODO: 小程序推送,记录日志
        }

        if (param.isSendPcMsg()) {
            String content = param.getContent();
//            String msg = this.getNoticeTitle(content);
            HrMessageListDTO messageListDTO = new HrMessageListDTO()
                .setContentType(2)
                .setContent(PcMessageContentEnum.MESSAGE_BILL_REIMB_APPLY.getKey())
                .setTitle("报销审核催办【" + content + "】")
                .setCreatedById(userDTO.getId())
                .setNoticeUserIds(Collections.singletonList(param.getUrgeUserId()));
            this.hrMessageListService.createHrMessageList(messageListDTO);
        }

        UserDTO urgeUserInfo = this.userRepository.getUserInFor(param.getUrgeUserId());
        message = userDTO.getRealName() + "向 (" + urgeUserInfo.getRoleName() + ")" + urgeUserInfo.getRealName() + "发起催办!";

        // 保存推送日志
        this.saveApplyOpLogs(applyDTO.getId(), applyDTO.getApplyUserId(), userDTO.getId(), message, "", false);

    }

    /**
     * 报销申请催办--批量操作
     *
     * @param param
     */
    @Override
    public Map<String, Object> batchApplyUrge(BillReimApplyUrgeDTO param) {
        List<String> fillList = new ArrayList<>();
        for (String applyId : param.getApplyIdList()) {
            //获取可催办的审批人
            List<UserDTO> urgeUsers = this.getUrgeUsers(applyId);
            if (CollectionUtils.isEmpty(urgeUsers)) {
                HrBillReimbursementApplyDTO applyDTO = this.hrBillReimbursementApplyRepository.getById(applyId);
                fillList.add(applyDTO.getTitle());
                continue;
            }
            String urgeUserId = urgeUsers.stream().map(UserDTO::getId).collect(Collectors.joining(","));
            param.setApplyId(applyId).setUrgeUserId(urgeUserId);
            this.reimbursementApplyUrge(param);
        }
        Map<String, Object> hashMap = new HashMap<>();
        if (fillList.size() == 0) {
            hashMap.put("checkCode", 200);
            hashMap.put("checkMsg", "报销单催办成功！");
        } else {
            hashMap.put("checkCode", 500);
            hashMap.put("checkMsg", "报销单 [" + String.join(",", fillList) + "] 催办失败！失败原因：没有下一步审批人的账号");
        }
        return hashMap;
    }

    /**
     * 获取通知标题
     *
     * @param content
     * @return
     */
    private String getNoticeTitle(String content) {
        if (StringUtils.isEmpty(content)) {
            return "";
        }
        return content.length() > NOTICE_MSG_MAX_LENGTH ? content.substring(0, 17) + "..." : content;
    }

    /**
     * 获取可通知的角色列表
     *
     * @return
     */
    @Override
    public List<RoleDTO> getNoticeRoles() {
        List<String> roleKeys = Arrays.asList(
            UserRoleTypeEnum.CUSTOMER_SERVICE_MANAGER.getKey(),
            UserRoleTypeEnum.TOTAL_MANAGER.getKey(),
            UserRoleTypeEnum.FINANCIAL_DIRECTOR.getKey(),
            UserRoleTypeEnum.ACCOUNTING.getKey(),
            UserRoleTypeEnum.CASHIER.getKey()
        );
        return this.roleRepository.getByRoleKeys(roleKeys);
    }

    /**
     * 报销申请:获取基础费用信息
     *
     * @param param
     * @return
     */
    @Override
    public HrBillReimbursementApplyDTO getBillTotalInfo(HrBillTotalDTO param) {
        if (StringUtils.isEmpty(param.getClientId()) || StringUtils.isEmpty(param.getPaymentDate())) {
            throw new CommonException("客户名称和缴费年月不能为空!");
        }
        HrBillTotalDTO billTotalDTO = this.hrBillTotalRepository.getByClientIdAndDate(param.getClientId(), param.getPayYear(), param.getPayMonthly());
        HrBillReimbursementApplyDTO result = new HrBillReimbursementApplyDTO();
        if (billTotalDTO == null) {
            return result;
        }
        List<HrBillReimbursementApplyDetailDTO> detailDTOS = this.assignmentDetailDTOList(billTotalDTO, result, null, null, null, null);
        result.setDetailDTOList(detailDTOS);
        return result;
    }

    @Override
    public List<HrBillReimbursementApplyDetailDTO> assignmentDetailDTOList(HrBillTotalDTO billTotalDTO, HrBillReimbursementApplyDTO result, List<HrBill> hrBills, Integer integer,
                                                                           List<HrBillInvoiceRecord> invoiceRecordList, HrClient rootParentClient) {
        List<String> billIds = hrBills.stream().map(HrBill::getId).collect(Collectors.toList());
        List<String> securityBillIds = hrBills.stream().filter(lst -> lst.getBillType().equals(BillEnum.BillType.SECURITY_BILL.getKey())).map(HrBill::getId).collect(Collectors.toList());
        List<HrBillReimbursementClientDTO> reimbursementClientDTOS = hrBillReimbursementApplyRepository.getAccountType(result.getPayYear(), result.getPayMonth(), billIds);
        Map<Integer, BigDecimal> hashMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(reimbursementClientDTOS)) {
            for (HrBillReimbursementClientDTO dto : reimbursementClientDTOS) {
                hashMap.put(dto.getAccountType(), dto.getAmount() == null ? BigDecimal.ZERO : dto.getAmount());
            }
        }
        List<HrBillReimbursementApplyDetailDTO> detailDTOS = new ArrayList<>();
        if (!hrBills.get(0).getBillType().equals(BillEnum.BillType.SINOPEC_EXPENSE_BILL.getKey())) {
            //流程自动化创建报销前置条件--该结算是否已经通过新建发起了报销
            if (integer == 0) {//结算单流程自动化
                BigDecimal socialSecurityTotal = BigDecimal.ZERO;
                BigDecimal medicalInsuranceTotal = BigDecimal.ZERO;
                BigDecimal accumulationFundTotal = BigDecimal.ZERO;
                // 代发工资 = 实发工资
                BigDecimal salary = billTotalDTO.getRealSalaryTotal();
                if (result.getAccountType() != null && result.getAccountType().equals(BillReimbApproveEnums.InvoiceTypeEnum.SPECIAL_CLIENT.getKey())) {
                    if (rootParentClient.getId().equals(SpecialBillClient.HAIER.getKey())) {
                        if (securityBillIds == null || securityBillIds.isEmpty()) {
                            throw new CommonException("结算单未绑定保障账单！");
                        }
                        billTotalDTO = hrBillTotalRepository.getBillTotalByBatchBill(securityBillIds);
                        salary = BigDecimal.ZERO;
                    }
                    //(单位+个人)养老、(单位+个人)失业、(单位)工伤、(单位)补充工伤在申请报销时都将并入代缴社保；
                    socialSecurityTotal = CalculateUtils.decimalListAddition(billTotalDTO.getUnitPensionTotal(),
                        billTotalDTO.getUnitUnemploymentTotal(),
                        billTotalDTO.getWorkInjuryTotal(),
                        billTotalDTO.getReplenishWorkInjuryExpenseTotal(),
                        billTotalDTO.getPersonalPensionTotal(),
                        billTotalDTO.getPersonalUnemploymentTotal());
                    //(单位+个人)医疗、(单位)生育、(单位+个人)大额医疗在申请报销时都将并入代缴医保。
                    medicalInsuranceTotal = CalculateUtils.decimalListAddition(billTotalDTO.getUnitMedicalTotal(),
                        billTotalDTO.getPersonalMedicalTotal(),
                        billTotalDTO.getUnitMaternityTotal(),
                        billTotalDTO.getUnitLargeMedicalExpenseTotal(),
                        billTotalDTO.getPersonalLargeMedicalExpenseTotal());
                    accumulationFundTotal = billTotalDTO.getAccumulationFundTotal();
                    //总金额= 代发工资+代缴社保+代缴医保+代缴公积金
                    // 添加费用明细, 代发工资, 代缴社保, 代缴公积金，代缴医保
                    detailDTOS.add(getApplyDetailDTO(BillReimbApproveEnums.InvoiceTypeEnum.DF_MEDICAL_INSURANCE.getKey(), this.getAmount(hashMap, BillReimbApproveEnums.InvoiceTypeEnum.DF_MEDICAL_INSURANCE.getKey(), medicalInsuranceTotal)));
                } else {
                    //报销类型添加其他
                    List<HrBill> collect = hrBills.stream().filter(lst -> lst.getBillType().equals(BillEnum.BillType.OTHER_BILL.getKey())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect)) {
                        if (collect.size() == hrBills.size()) {//只有其他账单费用明细内容只有其他费用
                            detailDTOS = new ArrayList<>();
                        }
                        for (HrBill hrBill : collect) {
                            HrBillTotal hrBillTotal = hrBillTotalRepository.selectOne(new QueryWrapper<HrBillTotal>().eq("bill_id", hrBill.getId()));
                            BigDecimal bigDecimal = CalculateUtils.decimalSubtraction(hrBillTotal.getChargeTotal(), hrBillTotal.getRefundTotal());
                            detailDTOS.add(getApplyDetailDTO(BillReimbApproveEnums.InvoiceTypeEnum.DF_OTHER.getKey(), bigDecimal));
                        }
                    }
                    accumulationFundTotal = billTotalDTO.getAccumulationFundTotal();
                    socialSecurityTotal = billTotalDTO.getSocialSecurityTotal();
                    // 总金额=代发工资+总公积金+总社保
                }
                // 添加费用明细, 代发工资, 代缴社保, 代缴公积金，代缴医保
                if (!rootParentClient.getId().equals(SpecialBillClient.HAIER.getKey())) {
                    //海尔代发工资单独报销
                    detailDTOS.add(getApplyDetailDTO(BillReimbApproveEnums.InvoiceTypeEnum.DF_SALARY.getKey(), this.getAmount(hashMap, BillReimbApproveEnums.InvoiceTypeEnum.DF_SALARY.getKey(), salary)));
                }
                detailDTOS.add(getApplyDetailDTO(BillReimbApproveEnums.InvoiceTypeEnum.DF_SOCIAL_SECURITY.getKey(), this.getAmount(hashMap, BillReimbApproveEnums.InvoiceTypeEnum.DF_SOCIAL_SECURITY.getKey(), socialSecurityTotal)));
                detailDTOS.add(getApplyDetailDTO(BillReimbApproveEnums.InvoiceTypeEnum.DF_ACCUMULATION_FOUND.getKey(), this.getAmount(hashMap, BillReimbApproveEnums.InvoiceTypeEnum.DF_ACCUMULATION_FOUND.getKey(), accumulationFundTotal)));
            } else if (integer == 1) {//开票通过
                Map<String, List<HrBillInvoiceRecord>> listMap = invoiceRecordList.stream().collect(Collectors.groupingBy(HrBillInvoiceRecord::getContent));
                for (String content : listMap.keySet()) {
                    BillInvoiceApproveEnums.InvoiceContent enumByKey = EnumUtils.getEnumByKey(BillInvoiceApproveEnums.InvoiceContent.class, content);
                    switch (enumByKey) {
                        case WAGE_INCOME:
                            if (!rootParentClient.getId().equals(SpecialBillClient.HAIER.getKey())) {
                                //海尔代发工资单独报销
                                detailDTOS.add(getApplyDetailDTO(BillReimbApproveEnums.InvoiceTypeEnum.DF_SALARY.getKey(), this.getAmount(hashMap, BillReimbApproveEnums.InvoiceTypeEnum.DF_SALARY.getKey(), billTotalDTO.getRealSalaryTotal())));
                            }
                            break;
                        case SOCIAL_SECURITY_INCOME:
                            //代缴医保 = (单位+个人)医疗+(单位+个人)生育+(单位+个人)大额医疗
                            BigDecimal medicalTotal = CalculateUtils.decimalListAddition(billTotalDTO.getUnitMedicalTotal(), billTotalDTO.getPersonalMedicalTotal(),
                                billTotalDTO.getUnitMaternityTotal(), billTotalDTO.getPersonalMaternityTotal(), billTotalDTO.getUnitLargeMedicalExpenseTotal(), billTotalDTO.getPersonalLargeMedicalExpenseTotal());
                            detailDTOS.add(getApplyDetailDTO(BillReimbApproveEnums.InvoiceTypeEnum.DF_MEDICAL_INSURANCE.getKey(), this.getAmount(hashMap, BillReimbApproveEnums.InvoiceTypeEnum.DF_MEDICAL_INSURANCE.getKey(), medicalTotal)));
                            //代缴社保 = 结算单社保总金额 - 代缴医保
                            BigDecimal socialTotal = CalculateUtils.decimalListSubstract(billTotalDTO.getSocialSecurityTotal(), medicalTotal);
                            detailDTOS.add(getApplyDetailDTO(BillReimbApproveEnums.InvoiceTypeEnum.DF_SOCIAL_SECURITY.getKey(), this.getAmount(hashMap, BillReimbApproveEnums.InvoiceTypeEnum.DF_SOCIAL_SECURITY.getKey(), socialTotal)));
                            break;
                        case ACCUMULATION_FUND_INCOME:
                            detailDTOS.add(getApplyDetailDTO(BillReimbApproveEnums.InvoiceTypeEnum.DF_ACCUMULATION_FOUND.getKey(), this.getAmount(hashMap, BillReimbApproveEnums.InvoiceTypeEnum.DF_ACCUMULATION_FOUND.getKey(), billTotalDTO.getAccumulationFundTotal())));
                            break;
                        case SOCIAL_SECURITY_ACCUMULATION_FUND://社保公积金只用于海尔开票
                            if (rootParentClient.getId().equals(SpecialBillClient.HAIER.getKey())) {
                                if (securityBillIds == null || securityBillIds.isEmpty()) {
                                    throw new CommonException("结算单未绑定保障账单！");
                                }
                                billTotalDTO = hrBillTotalRepository.getBillTotalByBatchBill(securityBillIds);
                                billTotalDTO.setRealSalaryTotal(BigDecimal.ZERO);
                            }
                            //(单位+个人)养老、(单位+个人)失业、(单位)工伤、(单位)补充工伤在申请报销时都将并入代缴社保；
                            BigDecimal socialSecurityTotal = CalculateUtils.decimalListAddition(billTotalDTO.getUnitPensionTotal(),
                                billTotalDTO.getUnitUnemploymentTotal(),
                                billTotalDTO.getWorkInjuryTotal(),
                                billTotalDTO.getReplenishWorkInjuryExpenseTotal(),
                                billTotalDTO.getPersonalPensionTotal(),
                                billTotalDTO.getPersonalUnemploymentTotal());
                            //(单位+个人)医疗、(单位)生育、(单位+个人)大额医疗在申请报销时都将并入代缴医保。
                            BigDecimal medicalInsuranceTotal = CalculateUtils.decimalListAddition(billTotalDTO.getUnitMedicalTotal(),
                                billTotalDTO.getPersonalMedicalTotal(),
                                billTotalDTO.getUnitMaternityTotal(),
                                billTotalDTO.getUnitLargeMedicalExpenseTotal(),
                                billTotalDTO.getPersonalLargeMedicalExpenseTotal());
                            detailDTOS.add(getApplyDetailDTO(BillReimbApproveEnums.InvoiceTypeEnum.DF_SOCIAL_SECURITY.getKey(), this.getAmount(hashMap, BillReimbApproveEnums.InvoiceTypeEnum.DF_SOCIAL_SECURITY.getKey(), socialSecurityTotal)));
                            detailDTOS.add(getApplyDetailDTO(BillReimbApproveEnums.InvoiceTypeEnum.DF_MEDICAL_INSURANCE.getKey(), this.getAmount(hashMap, BillReimbApproveEnums.InvoiceTypeEnum.DF_MEDICAL_INSURANCE.getKey(), medicalInsuranceTotal)));
                            detailDTOS.add(getApplyDetailDTO(BillReimbApproveEnums.InvoiceTypeEnum.DF_ACCUMULATION_FOUND.getKey(), this.getAmount(hashMap, BillReimbApproveEnums.InvoiceTypeEnum.DF_ACCUMULATION_FOUND.getKey(), billTotalDTO.getAccumulationFundTotal())));
                            break;
                        default:
                            if (result.getAccountType() != null && result.getAccountType().equals(BillReimbApproveEnums.InvoiceTypeEnum.SPECIAL_CLIENT.getKey())) {
                                break;
                            }
                            double amount = listMap.get(content).stream().mapToDouble(HrBillInvoiceRecord::getTotalAmount).sum();
                            detailDTOS.add(getApplyDetailDTO(BillReimbApproveEnums.InvoiceTypeEnum.DF_OTHER.getKey(), new BigDecimal(amount)));
                            break;
                    }
                }
            }
        } else {//中石化账单
            List<HrBillDetailItemsDTO> hrBillDetailItemsDTOList = hrFeeReviewRepository.getHrBillDetailItems(billIds);
            //代发工资对应职工薪酬小计，代发社保对应单位社保和个人社保，代发公积金对应单位公积金和个人公积金
            BigDecimal salary = hrBillDetailItemsDTOList.stream().filter(lst -> lst.getExpenseName().contains("职工薪酬") && lst.getExpenseType().equals(DynamicFeeTypesEnum.EMPLOYEE_COMPENSATION.getKey()))
                .map(HrBillDetailItemsDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal socialSecurityTotal = hrBillDetailItemsDTOList.stream().filter(lst -> lst.getExpenseType().equals(DynamicFeeTypesEnum.UNIT_INSURANCE.getKey()) || lst.getExpenseType().equals(DynamicFeeTypesEnum.INDIVIDUAL_INSURANCE.getKey()))
                .map(HrBillDetailItemsDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal accumulationFundTotal = hrBillDetailItemsDTOList.stream().filter(lst -> lst.getExpenseType().equals(DynamicFeeTypesEnum.UNIT_HOUSING_FUND.getKey()) || lst.getExpenseType().equals(DynamicFeeTypesEnum.INDIVIDUAL_HOUSING_FUND.getKey()))
                .map(HrBillDetailItemsDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            detailDTOS.add(getApplyDetailDTO(BillReimbApproveEnums.InvoiceTypeEnum.DF_SALARY.getKey(), this.getAmount(hashMap, BillReimbApproveEnums.InvoiceTypeEnum.DF_SALARY.getKey(), salary)));
            detailDTOS.add(getApplyDetailDTO(BillReimbApproveEnums.InvoiceTypeEnum.DF_SOCIAL_SECURITY.getKey(), this.getAmount(hashMap, BillReimbApproveEnums.InvoiceTypeEnum.DF_SOCIAL_SECURITY.getKey(), socialSecurityTotal)));
            detailDTOS.add(getApplyDetailDTO(BillReimbApproveEnums.InvoiceTypeEnum.DF_ACCUMULATION_FOUND.getKey(), this.getAmount(hashMap, BillReimbApproveEnums.InvoiceTypeEnum.DF_ACCUMULATION_FOUND.getKey(), accumulationFundTotal)));
        }
        BigDecimal amount = detailDTOS.stream().filter(lst -> lst.getAmount() != null).map(HrBillReimbursementApplyDetailDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        result.setAmount(amount);
        if (BigDecimalCompare.of(amount).eq(BigDecimal.ZERO)) {
            result.setIsShow(1);
        }
        return detailDTOS;
    }

    private BigDecimal getAmount(Map<Integer, BigDecimal> hashMap, Integer key, BigDecimal amount) {
        if (CollectionUtils.isEmpty(hashMap)) {
            return amount;
        }
        return CalculateUtils.decimalSubtraction(amount, hashMap.get(key));
    }

    /**
     * 报销申请下载报销单以及附件
     *
     * @param ids
     * @return
     */
    @Override
    public String downloadReimbursementForm(List<String> ids) {
        List<File> fileList = new ArrayList<>();
        List<String> pathList = new ArrayList<>();
        List<HrBillReimbursementApplyDTO> hrBillReimbursementApplyDTOS = hrBillReimbursementApplyRepository.selectByIdList(ids);
        List<HrBillReimbursementApplyDTO> collect = hrBillReimbursementApplyDTOS.stream().filter(lst -> lst.getAccountType() == null).collect(Collectors.toList());
        List<HrBillReimbursementApplyDTO> collect1 = hrBillReimbursementApplyDTOS.stream().filter(lst -> lst.getAccountType() != null).collect(Collectors.toList());
        //批量发起报销和单个报销不可一起下载报销单
        if (CollectionUtils.isNotEmpty(collect) && CollectionUtils.isNotEmpty(collect1)) {
            throw new CommonException("批量发起报销和单个报销不可一起下载报销单!");
        }
        //0 单个报销 1批量报销
        Integer flag = CollectionUtils.isNotEmpty(collect1) ? 1 : 0;
        if (flag == 1) {
            Map<Integer, List<HrBillReimbursementApplyDTO>> listMap = collect1.stream().collect(Collectors.groupingBy(HrBillReimbursementApplyDTO::getAccountType));
            if (listMap.size() > 1) {
                throw new CommonException("不同费用报销不可一起下载!");
            }
        }
        this.generateReimbursementDocument(hrBillReimbursementApplyDTOS, flag, fileList, pathList);

        List<HrBillReimbursementApplyDetailDTO> detailDTOS = this.hrBillReimbursementApplyDetailRepository.getByApplyIdList(ids);
        List<HrBillReimbursementClientDTO> reimbursementClients = hrBillReimbursementApplyDetailRepository.findListByApplyId(ids);

        this.generateReimbursementDocumentDetail(detailDTOS, reimbursementClients, flag, 1, fileList, pathList);
        if (flag == 1) {
            this.generateReimbursementDocumentDetail(detailDTOS, reimbursementClients, flag, 2, fileList, pathList);
        }
        String uploadFile = this.hrAppendixService.zipAndUploadFile(fileList, "报销单");
        pathList.add(uploadFile);
        pathList.forEach(lst -> {
            FileUtil.deleteTempFile(lst);
        });
        return uploadFile;
    }

    /**
     * 生成报销单附件
     *
     * @param detailDTOS
     * @param reimbursementClients
     * @param flag                 0 单个报销 1批量报销附件
     * @param fileList
     * @param pathList
     * @return
     */
    private void generateReimbursementDocumentDetail(List<HrBillReimbursementApplyDetailDTO> detailDTOS, List<HrBillReimbursementClientDTO> reimbursementClients, Integer flag, Integer fileFlag, List<File> fileList, List<String> pathList) {
        XSSFWorkbook wb = new XSSFWorkbook();
        ZlSheet sheet = new ZlSheet(wb);
        sheet.nextRow().nextRow(45)
            .createCell(sheet.newMergeCell(15, "公司报销单附件", true, (short) 20, false).setBackground(IndexedColors.BLUE))
            .nextRow(45)
            .createNextCell("摘要", true, 45, (short) 13)
            .createCell(4, "预算报表", true, 6, (short) 13)
            .createCell(6, "预算指标", true, 7, (short) 13)
            .createCell(8, "发票金额(含税)", true, 10, (short) 13)
            .createCell(10, "专票税额(其他填0)", true, 11, (short) 13)
            .createCell(12, "申请金额", true, 9, (short) 13)
            .createCell(15, "应付供应商", true, 13, (short) 13);
        BigDecimal reduce = BigDecimal.ZERO;
        if (flag == 0) {
            for (HrBillReimbursementApplyDetailDTO detailDTO : detailDTOS) {
                sheet.nextRow(45)
                    .createNextCell(detailDTO.getTitle(), false, 45, (short) 13)
                    .createCell(4, "其他", false, 6, (short) 13)
                    .createCell(6, detailDTO.getInvoiceTypeName(), false, 7, (short) 13)
                    .createCell(8, detailDTO.getAmount(), false, 10, (short) 13)
                    .createCell(10, 0, false, 11, (short) 13)
                    .createCell(12, detailDTO.getAmount(), false, 7, (short) 13)
                    .createCell(15, detailDTO.getClientName(), false, 13, (short) 13);
                reduce = reduce.add(detailDTO.getAmount());
            }

        } else if (flag == 1) {
            if (fileFlag == 1) {
                //新建发起社保、医保、公积金
                Map<String, List<HrBillReimbursementClientDTO>> listMap = reimbursementClients.stream().filter(lst -> lst.getAccountNumber() != null).collect(Collectors.groupingBy(HrBillReimbursementClientDTO::getAccountNumber));
                for (String accountNumber : listMap.keySet()) {
                    List<HrBillReimbursementClientDTO> dtoList = listMap.get(accountNumber);
                    BigDecimal amount = dtoList.stream().filter(lst -> lst.getAmount() != null).map(HrBillReimbursementClientDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    String valueByKey = BillReimbApproveEnums.InvoiceTypeEnum.getValueByKey(dtoList.get(0).getAccountType());
                    sheet.nextRow(45)
                        .createNextCell(valueByKey.substring(2) + "账户-" + accountNumber, false, 45, (short) 13)
                        .createCell(4, "其他", false, 6, (short) 13)
                        .createCell(6, valueByKey, false, 7, (short) 13)
                        .createCell(8, amount, false, 10, (short) 13)
                        .createCell(10, 0, false, 11, (short) 13)
                        .createCell(12, amount, false, 7, (short) 13)
                        .createCell(15, dtoList.size() + "个", false, 13, (short) 13);
                    reduce = reduce.add(amount);
                }
                //代发工资
                List<HrBillReimbursementClientDTO> salaryDTOList = reimbursementClients.stream().filter(lst -> lst.getAccountType().equals(BillReimbApproveEnums.InvoiceTypeEnum.DF_SALARY.getKey())).collect(Collectors.toList());
                if (salaryDTOList != null && !salaryDTOList.isEmpty()) {
                    BigDecimal amount = salaryDTOList.stream().filter(lst -> lst.getAmount() != null).map(HrBillReimbursementClientDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    sheet.nextRow(45)
                        .createNextCell(BillReimbApproveEnums.InvoiceTypeEnum.DF_SALARY.getValue(), false, 45, (short) 13)
                        .createCell(4, "其他", false, 6, (short) 13)
                        .createCell(6, BillReimbApproveEnums.InvoiceTypeEnum.DF_SALARY.getValue(), false, 7, (short) 13)
                        .createCell(8, amount, false, 10, (short) 13)
                        .createCell(10, 0, false, 11, (short) 13)
                        .createCell(12, amount, false, 7, (short) 13)
                        .createCell(15, salaryDTOList.size() + "个", false, 13, (short) 13);
                    reduce = reduce.add(amount);
                }
            } else {
                for (HrBillReimbursementClientDTO detailDTO : reimbursementClients) {
                    String value = detailDTO.getAccountType().equals(BillReimbApproveEnums.InvoiceTypeEnum.DF_SALARY.getKey()) ? detailDTO.getClientName() : detailDTO.getAccountNumber();
                    sheet.nextRow(45)
                        .createNextCell(detailDTO.getClientName(), false, 45, (short) 13)
                        .createCell(4, "其他", false, 6, (short) 13)
                        .createCell(6, detailDTO.getInvoiceTypeName(), false, 7, (short) 13)
                        .createCell(8, detailDTO.getAmount(), false, 10, (short) 13)
                        .createCell(10, 0, false, 11, (short) 13)
                        .createCell(12, detailDTO.getAmount(), false, 7, (short) 13)
                        .createCell(15, value, false, 13, (short) 13);
                    reduce = reduce.add(detailDTO.getAmount() == null ? BigDecimal.ZERO : detailDTO.getAmount());
                }
            }
        }
        sheet.nextRow(45)
            .createNextCell("合计", true, 45, (short) 13)
            .createCell(8, reduce, true, 10, (short) 13)
            .createCell(10, "大写（人民币）", true, 12, (short) 13)
            .createCell(15, Convert.digitToChinese(reduce), true, 11, (short) 13);
        String fileName = fileFlag == 1 ? "公司报销单附件" : "公司报销单附二";
        String filePath = sheet.writeFile(tempPath + fileName + ".xlsx");
        //加载Excel文档
        Workbook workbook = new Workbook();
        workbook.loadFromFile(filePath);
        //设置转换后的PDF页面高宽适应工作表的内容大小
        //调用方法保存为PDF格式
        String pdfName = tempPath + fileName + ".pdf";
        workbook.setCustomFontFilePaths(new String[]{fontPath});
        workbook.saveToFile(pdfName, FileFormat.PDF);
        fileList.add(new File((filePath)));
        fileList.add(new File((pdfName)));
        pathList.add(pdfName);
    }

    /**
     * 生成报销单
     *
     * @param hrBillReimbursementApplyDTOS
     * @param flag                         0单个发起报销  1 批量发起报销
     * @param fileList
     * @param pathList
     * @return
     */
    private void generateReimbursementDocument(List<HrBillReimbursementApplyDTO> hrBillReimbursementApplyDTOS, Integer flag, List<File> fileList, List<String> pathList) {
        XSSFWorkbook wb = new XSSFWorkbook();
        ZlSheet sheet = new ZlSheet(wb);
        sheet.nextRow().nextRow().nextRow(34)
            .createCell(sheet.newMergeCell(13, "青岛市黄岛人力资源有限公司", true, (short) 20, true).setBackground(IndexedColors.BLUE))
            .nextRow(34)
            .createCell(sheet.newMergeCell(13, "报    销    单", true, (short) 20, true).setBackground(IndexedColors.BLUE))
            .nextRow(45)
            .createNextCell(sheet.newCell("部门", true, 25, (short) 14).setBackground(IndexedColors.BLUE))
            .createCell(7, "客服部", false, (short) 15)
            .createCell(10, "日期", true, (short) 15)
            .createCell(13, LocalDate.now(), false, (short) 15)
            .nextRow(45)
            .createCell(3, "摘要", true, 14, (short) 13)
            .createCell(5, "发票金额(含税)", true, 11, (short) 13)
            .createCell(7, "专票税额(其他填0)", true, 15, (short) 13)
            .createCell(9, "申请金额", true, 13, (short) 13)
            .createCell(11, "收款单位", true, 15, (short) 13)
            .createCell(13, "项目", true, 0, (short) 13);
        BigDecimal reduce = hrBillReimbursementApplyDTOS.stream().map(HrBillReimbursementApplyDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        List<String> collect = hrBillReimbursementApplyDTOS.stream().map(HrBillReimbursementApplyDTO::getClientName).distinct().collect(Collectors.toList());
        List<String> ids = hrBillReimbursementApplyDTOS.stream().map(HrBillReimbursementApplyDTO::getId).distinct().collect(Collectors.toList());
        HrBillReimbursementApplyDTO hrBillReimbursementApplyDTO = hrBillReimbursementApplyDTOS.get(0);
        String substring = "";
        if (flag == 1) {
            HrBillReimbursementApplyDTO applyDTO = hrBillReimbursementApplyDTOS.get(0);
            HrBillReimbursementClient selectOne = hrBillReimbursementClientRepository.selectOne(new QueryWrapper<HrBillReimbursementClient>().eq("apply_id", applyDTO.getId()).last("LIMIT 1"));
            HrClient hrClient = hrClientService.getById(selectOne.getClientId());
            substring = "代缴" + hrClient.getClientName() + "等单位"
                + applyDTO.getPayYear() + (applyDTO.getPayMonth() < 9 ? "0" + applyDTO.getPayMonth() : applyDTO.getPayMonth())
                + BillReimbApproveEnums.InvoiceTypeEnum.getValueByKey(applyDTO.getAccountType()).substring(2);
        } else {
            substring = hrBillReimbursementApplyDTO.getTitle().substring(hrBillReimbursementApplyDTO.getClientName().length()).replaceFirst("^0*", "");
        }
        String clientName = hrBillReimbursementApplyDTO.getClientName();
        for (int i = 0; i < 3; i++) {
            if (i == 0) {
                sheet.nextRow(45)
                    .createCell(3, substring, false, (short) 13)
                    .createCell(5, reduce, false, (short) 13)
                    .createCell(7, 0, false, (short) 13)
                    .createCell(9, reduce, false, (short) 13)
                    .createCell(11, collect.size() > 1 ? "其他" : clientName == null ? "其他" : clientName, false, (short) 13)
                    .createCell(13, "其他", false, (short) 13);
            } else {
                sheet.nextRow(45)
                    .createCell(3, "", false, (short) 13)
                    .createCell(5, "", false, (short) 13)
                    .createCell(7, "", false, (short) 13)
                    .createCell(9, "", false, (short) 13)
                    .createCell(11, "", false, (short) 13)
                    .createCell(13, "", false, (short) 13);
            }
        }
        List<String> stringList = hrBillReimbursementApplyDTOS.stream().filter(lst -> lst.getApplyRealName() != null).map(HrBillReimbursementApplyDTO::getApplyRealName).distinct().collect(Collectors.toList());
        sheet.nextRow(45)
            .createNextCell("合计", true, 25, (short) 13)
            .createCell(6, reduce, true, 14, (short) 13)
            .createCell(8, "大写（人民币）", true, 14, (short) 13)
            .createCell(13, Convert.digitToChinese(reduce), true, 0, (short) 13)
            .nextRow(45)
            .createNextCell("经办人", true, 25, (short) 13)
            .createCell(6, stringList.size() == 1 ? stringList.get(0) : "", true, 14, (short) 13)
            .createCell(8, "部门/子公司负责人", true, 14, (short) 13)
            .createCell(13, reviewerInfo(ids, UserRoleTypeEnum.CUSTOMER_SERVICE_MANAGER.getKey(), "审核通过"), true, 0, (short) 13)
            .nextRow(45)
            .createNextCell("财务管理部", true, 14, (short) 13)
            .createCell(4, "", true, 13, (short) 13)
            .createCell(6, "", true, 13, (short) 13)
            .createCell(8, "合规合约部", true, 14, (short) 13)
            .createCell(11, "", true, 0, (short) 13)
            .createCell(13, "", true, 0, (short) 13)
            .nextRow(45)
            .createNextCell("主管领导", true, 14, (short) 13)
            .createCell(6, "", true, 13, (short) 13)
            .createCell(8, "总裁", true, 14, (short) 13)
            .createCell(13, "", true, 0, (short) 13)
            .nextRow(45)
            /*.createNextCell("监事会主席",true,25,(short)13)
            .createCell(13,"",true,14,(short)13)
            .nextRow(45)*/
            .createNextCell("董事长", true, 25, (short) 13)
            .createCell(13, "", true, 0, (short) 13);
        String filePath = sheet.writeFile(tempPath + "集团报销单.xlsx");
        //加载Excel文档
        Workbook workbook = new Workbook();
        workbook.loadFromFile(filePath);
        //调用方法保存为PDF格式
        String pdfName = tempPath + "集团报销单.pdf";
        workbook.setCustomFontFilePaths(new String[]{fontPath});
        workbook.saveToFile(pdfName, FileFormat.PDF);
        fileList.add(new File((filePath)));
        fileList.add(new File((pdfName)));
        pathList.add(pdfName);
    }

    /**
     * 查看审核人信息
     *
     * @param ids     申请ID
     * @param roleKey 角色
     * @param message 审核信息
     * @return
     */
    private String reviewerInfo(List<String> ids, String roleKey, String message) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
        String createdDate = formatter.format(LocalDate.now());
        List<HrApplyOpLogsDTO> hrApplyOpLogsDTOList = hrApplyOpLogsRepository.reviewerInfo(ids, roleKey, message);
        if (CollectionUtils.isEmpty(hrApplyOpLogsDTOList)) {
            return "";
        }
        if (hrApplyOpLogsDTOList.size() < ids.size()) {
            return "";
        }
        List<String> collect = hrApplyOpLogsDTOList.stream().map(HrApplyOpLogsDTO::getRealName).distinct().collect(Collectors.toList());
        return collect.size() == 1 ? collect.get(0) + "\t" + createdDate : "";
    }

    private HrBillReimbursementApplyDetailDTO getApplyDetailDTO(Integer invoiceType, BigDecimal amount) {
        HrBillReimbursementApplyDetailDTO detailDTO = new HrBillReimbursementApplyDetailDTO()
            .setInvoiceType(invoiceType)
            .setAmount(amount);
        return detailDTO;
    }

    /**
     * 会计确认
     *
     * @param userDTO      用户信息
     * @param approveEnums 当前流程
     * @param applyDTO     报销审核对象
     */
    private void confirmByAccounting(JWTUserDTO userDTO, BillReimbApproveEnums approveEnums, HrBillReimbursementApplyDTO applyDTO) {
        boolean hasPermission = UserRoleTypeEnum.ACCOUNTING.getKey().equals(userDTO.getCurrentRoleKey());
        this.checkPermission(hasPermission, approveEnums);
        applyDTO.setApproveStatus(BillReimbApproveEnums.SUCCESS.getKey());

        //账单信息展示在工资发放页面
        HrFeeReview hrFeeReview = hrFeeReviewRepository.selectById(applyDTO.getBillId());
        if (hrFeeReview != null) {
            List<String> billIdList = Arrays.asList(hrFeeReview.getBillId().split(","));
            hrBillRepository.updateSalaryPaymentState(billIdList, 1);
        }
        // TODO: 调用NC,生成凭证

        // 保存日志
        this.saveApplyOpLogs(applyDTO.getId(), applyDTO.getApplyUserId(), userDTO.getId(), "会计确认", applyDTO.getRejectMsg(), applyDTO.isWxMini());
    }

    /**
     * 董事长审批resignationDate,
     *
     * @param userDTO      用户信息
     * @param approveEnums 当前流程
     * @param applyDTO     报销审核对象
     */
    private void auditByChairman(JWTUserDTO userDTO, BillReimbApproveEnums approveEnums, HrBillReimbursementApplyDTO applyDTO) {
        boolean hasPermission = UserRoleTypeEnum.CHAIRMAN.getKey().equals(userDTO.getCurrentRoleKey());
        this.checkPermission(hasPermission, approveEnums);
        if (AUDIT_SUCCESS.equals(applyDTO.getAuditType())) {
            // 通过
            applyDTO.setApproveStatus(BillReimbApproveEnums.ON_ACCOUNTING.getKey());
            // 保存日志
            this.saveApplyOpLogs(applyDTO.getId(), applyDTO.getApplyUserId(), userDTO.getId(),
                MESSAGE_AUDIT_SUCCESS, applyDTO.getRejectMsg(), applyDTO.isWxMini());
        } else if (AUDIT_REJECT.equals(applyDTO.getAuditType())) {
            // 拒绝
            this.commonReject(applyDTO, userDTO);
        }
    }


    /**
     * 监事会主席审批
     *
     * @param userDTO      用户信息
     * @param approveEnums 当前流程
     * @param applyDTO     报销审核对象
     */
    private void auditBySuperVisors(JWTUserDTO userDTO, BillReimbApproveEnums approveEnums, HrBillReimbursementApplyDTO applyDTO) {
        boolean hasPermission = UserRoleTypeEnum.SUPERVISORY_BOARD_CHAIRMAN.getKey().equals(userDTO.getCurrentRoleKey());
        this.checkPermission(hasPermission, approveEnums);
        if (AUDIT_SUCCESS.equals(applyDTO.getAuditType())) {
            // 通过
            applyDTO.setApproveStatus(BillReimbApproveEnums.ON_CHAIRMAN.getKey());
            // 保存日志
            this.saveApplyOpLogs(applyDTO.getId(), applyDTO.getApplyUserId(), userDTO.getId(),
                MESSAGE_AUDIT_SUCCESS, applyDTO.getRejectMsg(), applyDTO.isWxMini());
        } else if (AUDIT_REJECT.equals(applyDTO.getAuditType())) {
            // 拒绝
            this.commonReject(applyDTO, userDTO);
        }
    }

    /**
     * 总裁审批
     *
     * @param userDTO      用户信息
     * @param approveEnums 当前流程
     * @param applyDTO     报销审核对象
     */
    private void auditByCEO(JWTUserDTO userDTO, BillReimbApproveEnums approveEnums, HrBillReimbursementApplyDTO applyDTO) {
        boolean hasPermission = UserRoleTypeEnum.CEO.getKey().equals(userDTO.getCurrentRoleKey());
        this.checkPermission(hasPermission, approveEnums);
        if (AUDIT_SUCCESS.equals(applyDTO.getAuditType())) {
            // 通过
            applyDTO.setApproveStatus(BillReimbApproveEnums.ON_SUPERVISORS.getKey());
            // 保存日志
            this.saveApplyOpLogs(applyDTO.getId(), applyDTO.getApplyUserId(), userDTO.getId(),
                MESSAGE_AUDIT_SUCCESS, applyDTO.getRejectMsg(), applyDTO.isWxMini());
        } else if (AUDIT_REJECT.equals(applyDTO.getAuditType())) {
            // 拒绝
            this.commonReject(applyDTO, userDTO);
        }

    }

    /**
     * 财务负责人审批
     *
     * @param userDTO      用户信息
     * @param approveEnums 当前流程
     * @param applyDTO     报销审核对象
     */
    private void auditByFinancialDirector(JWTUserDTO userDTO, BillReimbApproveEnums approveEnums, HrBillReimbursementApplyDTO applyDTO) {
        boolean hasPermission = UserRoleTypeEnum.FINANCIAL_DIRECTOR.getKey().equals(userDTO.getCurrentRoleKey());
        this.checkPermission(hasPermission, approveEnums);
        if (AUDIT_SUCCESS.equals(applyDTO.getAuditType())) {
            // 通过
            applyDTO.setApproveStatus(BillReimbApproveEnums.ON_CEO.getKey());
            // 保存日志
            this.saveApplyOpLogs(applyDTO.getId(), applyDTO.getApplyUserId(), userDTO.getId(),
                MESSAGE_AUDIT_SUCCESS, applyDTO.getRejectMsg(), applyDTO.isWxMini());
        } else if (AUDIT_REJECT.equals(applyDTO.getAuditType())) {
            // 拒绝
            this.commonReject(applyDTO, userDTO);
        }

    }

    /**
     * 总经理审批
     *
     * @param userDTO      用户信息
     * @param approveEnums 当前流程
     * @param applyDTO     报销审核对象
     */
    private void auditByGeneralManager(JWTUserDTO userDTO, BillReimbApproveEnums approveEnums, HrBillReimbursementApplyDTO applyDTO) {
        boolean hasPermission = UserRoleTypeEnum.TOTAL_MANAGER.getKey().equals(userDTO.getCurrentRoleKey());
        this.checkPermission(hasPermission, approveEnums);
        if (AUDIT_SUCCESS.equals(applyDTO.getAuditType())) {
            // 通过
            applyDTO.setApproveStatus(BillReimbApproveEnums.ON_ACCOUNTING.getKey());
            // 保存日志
            this.saveApplyOpLogs(applyDTO.getId(), applyDTO.getApplyUserId(), userDTO.getId(),
                MESSAGE_AUDIT_SUCCESS, applyDTO.getRejectMsg(), applyDTO.isWxMini());
        } else if (AUDIT_REJECT.equals(applyDTO.getAuditType())) {
            // 拒绝
            this.commonReject(applyDTO, userDTO);
        }

    }

    /**
     * 客服经理审批
     *
     * @param userDTO      用户信息
     * @param approveEnums 当前流程
     * @param applyDTO     报销审核对象
     */
    private void auditByCustomerManager(JWTUserDTO userDTO, BillReimbApproveEnums approveEnums, HrBillReimbursementApplyDTO applyDTO) {
        boolean hasPermission = UserRoleTypeEnum.CUSTOMER_SERVICE_MANAGER.getKey().equals(userDTO.getCurrentRoleKey());
        this.checkPermission(hasPermission, approveEnums);
        if (AUDIT_SUCCESS.equals(applyDTO.getAuditType())) {
            // 通过
            applyDTO.setApproveStatus(BillReimbApproveEnums.ON_ACCOUNTING.getKey());
            // 保存日志
            this.saveApplyOpLogs(applyDTO.getId(), applyDTO.getApplyUserId(), userDTO.getId(),
                MESSAGE_AUDIT_SUCCESS, applyDTO.getRejectMsg(), applyDTO.isWxMini());
        } else if (AUDIT_REJECT.equals(applyDTO.getAuditType())) {
            // 拒绝
            this.commonReject(applyDTO, userDTO);
        }

    }

    /**
     * 推送站内消息
     *
     * @param createUserId
     * @param noticeRoles
     * @param title
     */
    private void sendMsg(String createUserId, String noticeRoles, String title) {
        if (StringUtils.isNotEmpty(noticeRoles)) {
            HrMessageListDTO messageListDTO = new HrMessageListDTO()
                .setCreatedById(createUserId)
                .setTitle(title)
                .setContent(PcMessageContentEnum.MESSAGE_BILL_REIMB_APPLY.getKey())
                .setContentType(2)
                .setRoleList(Arrays.asList(noticeRoles.split(",")));
            this.hrMessageListService.createHrMessageList(messageListDTO);
        }
    }

    /**
     * 通用拒绝方法
     *
     * @param applyDTO
     * @param userDTO
     */
    private void commonReject(HrBillReimbursementApplyDTO applyDTO, JWTUserDTO userDTO) {
        applyDTO.setApproveStatus(BillReimbApproveEnums.REJECT.getKey());
        String message = MESSAGE_AUDIT_REJECT;
//        String noticeTitle = this.getNoticeTitle(applyDTO.getRejectMsg());
        String noticeTitle = applyDTO.getTitle();
        this.sendMsg(userDTO.getId(), applyDTO.getNoticeRoles(), "报销申请【" + noticeTitle + "】 " + message);
        // 保存日志
        this.saveApplyOpLogs(applyDTO.getId(), applyDTO.getApplyUserId(), userDTO.getId(), message, applyDTO.getRejectMsg(), applyDTO.isWxMini());
    }

    /**
     * 保存操作日志
     *
     * @param applyId     报销申请id
     * @param applyUserId 申请人id
     * @param userId      审核人id
     * @param message     审批信息
     * @param isWxmini    是否为小程序 true 是
     */
    private void saveApplyOpLogs(String applyId, String applyUserId, String userId, String message, String remark, boolean isWxmini) {
        HrApplyOpLogsDTO logsDTO = new HrApplyOpLogsDTO()
            .setApplyId(applyId)
            .setApplyStaffId(applyUserId)
            .setCheckerType(isWxmini)
            .setServeType(ServiceCenterEnum.REIMBURSEMENT_APPLY.getKey())
            .setCheckerId(userId)
            .setMessage(message)
            .setRemark(remark);

        this.hrApplyOpLogsService.createHrApplyOpLogs(logsDTO);
    }

    /**
     * 校验操作权限
     *
     * @param hasPermission
     * @param approveEnums
     */
    private void checkPermission(boolean hasPermission, BillReimbApproveEnums approveEnums) {
        if (!hasPermission) {
            throw new CommonException("当前流程为[" + approveEnums.getName() + "],您没有审核权限!");
        }
    }

    /**
     * 生成报销申请信息
     *
     * @param hrBillReimbursementApplyDTO
     * @return
     */
    @Override
    public HrBillReimbursementApplyDTO generateHrBillReimbursementApply(HrBillReimbursementApplyDTO hrBillReimbursementApplyDTO) {
        JWTUserDTO userDTO = SecurityUtils.getJwtUser();
        HrBillReimbursementApplyDTO result = new HrBillReimbursementApplyDTO();
        result.setApplyUserId(userDTO.getId())
            .setApplyDate(LocalDate.now())
            .setPaymentDate(hrBillReimbursementApplyDTO.getPaymentDate())
            .setPayYear(hrBillReimbursementApplyDTO.getPayYear())
            .setPayMonth(hrBillReimbursementApplyDTO.getPayMonth())
            .setFlag(false)
            .setReimbursementState(BillInvoiceApproveEnums.InvoiceState.INVOICE_RECORD.getKey())
            .setReimbursementLockState(BillInvoiceApproveEnums.InvoiceLockState.LOCKED.getKey())
            .setAccountType(hrBillReimbursementApplyDTO.getAccountType())
            .setPaymentAccountType(hrBillReimbursementApplyDTO.getPaymentAccountType())
            .setTitle(hrBillReimbursementApplyDTO.getTitle());
        ;
        BigDecimal amount = BigDecimal.ZERO;
        if (!hrBillReimbursementApplyDTO.getAccountType().equals(BillReimbApproveEnums.InvoiceTypeEnum.DF_SALARY.getKey())) {
            BillReimbApproveEnums.InvoiceTypeEnum enumByKey = EnumUtils.getEnumByKey(BillReimbApproveEnums.InvoiceTypeEnum.class, hrBillReimbursementApplyDTO.getAccountType());
            List<String> accountIds = hrBillReimbursementApplyDTO.getAccountIds();
            if (hrBillReimbursementApplyDTO.getPaymentAccountType() == 2) {
                result.setPaymentAccountId(accountIds.get(0));
            }
            List<HrPlatformAccount> hrPlatformAccounts = platformAccountRepository.selectBatchIds(accountIds);

            QueryWrapper<HrClient> wrapper = new QueryWrapper<>();
            switch (enumByKey) {
                case DF_SOCIAL_SECURITY:
                    wrapper.in("social_security_account_id", accountIds);
                    break;
                case DF_MEDICAL_INSURANCE:
                    wrapper.in("medical_insurance_account_id", accountIds);
                    break;
                case DF_ACCUMULATION_FOUND:
                    wrapper.in("provident_fund_account_id", accountIds);
                    break;
            }
            List<HrClient> hrClientList = hrClientService.list(wrapper);
            if (CollectionUtils.isEmpty(hrClientList)) {
                throw new CommonException("没有符合创建报销申请的客户信息");
            }
            List<String> clientIdList = hrClientList.stream().map(HrClient::getId).collect(Collectors.toList());
            //创建报销的结算单ID
            List<String> feeReviewIds = this.getFeeReviewId(hrBillReimbursementApplyDTO);
            //所有审核通过没有创建报销单的结算单信息
            List<HrFeeReviewDTO> hrFeeReviewDTOS = hrFeeReviewRepository.selectFeeReview(hrBillReimbursementApplyDTO.getPaymentDate(), feeReviewIds);
            if (CollectionUtils.isEmpty(hrFeeReviewDTOS)) {
                throw new CommonException("没有符合创建报销申请的结算单信息");
            }
            List<String> billIds = new ArrayList<>();
            for (HrFeeReviewDTO hrFeeReviewDTO : hrFeeReviewDTOS) {
                List<String> asList = Arrays.asList(hrFeeReviewDTO.getBillId().split(","));
                billIds.addAll(asList);
            }
            List<HrBill> hrBillList = hrBillRepository.selectList(new QueryWrapper<HrBill>()
                .eq("bill_type", BillEnum.BillType.SECURITY_BILL.getKey())
                .eq("pay_year", hrBillReimbursementApplyDTO.getPayYear())
                .eq("pay_monthly", hrBillReimbursementApplyDTO.getPayMonth())
                .in("id", billIds)
                .in("client_id", clientIdList));
            if (CollectionUtils.isEmpty(hrBillList)) {
                throw new CommonException("没有符合创建报销申请的账单信息");
            }
            List<HrBillDetailDTO> hrBillDetailDTOList = hrBillDetailRepository.getListByBillIdBatch(billIds, 1);
            List<HrBillReimbursementClientDTO> hrBillReimbursementClientDTOS = new ArrayList<>();
            for (HrBill hrBill : hrBillList) {
                List<HrBillDetailDTO> detailDTOList = hrBillDetailDTOList.stream().filter(lst -> lst.getBillId().equals(hrBill.getId())).collect(Collectors.toList());
                HrFeeReviewDTO feeReviewDTO = hrFeeReviewDTOS.stream().filter(ls -> ls.getBillId().contains(hrBill.getId())).findFirst().orElse(null);
                HrClient hrClient = hrClientList.stream().filter(ls -> ls.getId().equals(hrBill.getClientId())).findFirst().orElse(null);
                HrBillReimbursementClientDTO hrBillReimbursementClient = new HrBillReimbursementClientDTO();
                hrBillReimbursementClient
                    .setBillId(hrBill.getId())
                    .setFeeReviewId(feeReviewDTO.getId())
                    .setClientName(hrClient.getClientName())
                    .setClientId(hrBill.getClientId());
                BigDecimal bigDecimal = BigDecimal.ZERO;
                switch (enumByKey) {
                    case DF_SOCIAL_SECURITY:
                    case DF_MEDICAL_INSURANCE:
                        for (HrBillDetailDTO ls : detailDTOList) {
                            bigDecimal = CalculateUtils.decimalListAddition(bigDecimal, ls.getUnitMedical(), ls.getUnitMaternity(), ls.getUnitLargeMedicalExpense(),
                                ls.getPersonalMedical(), ls.getPersonalMaternity(), ls.getPersonalLargeMedicalExpense());
                        }
                        if (hrBillReimbursementApplyDTO.getAccountType().equals(BillReimbApproveEnums.InvoiceTypeEnum.DF_SOCIAL_SECURITY.getKey())) {
                            //社保总金额中包含医保金额，须扣减医保金额
                            hrBillReimbursementClient.setAccountId(hrClient.getSocialSecurityAccountId());
                            BigDecimal socialSecurityTotal = detailDTOList.stream().filter(ls -> ls.getSocialSecurityTotal() != null).map(HrBillDetailDTO::getSocialSecurityTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
                            bigDecimal = CalculateUtils.decimalSubtraction(socialSecurityTotal, bigDecimal);
                        } else {
                            hrBillReimbursementClient.setAccountId(hrClient.getMedicalInsuranceAccountId());
                        }
                        break;
                    case DF_ACCUMULATION_FOUND:
                        bigDecimal = detailDTOList.stream().filter(ls -> ls.getAccumulationFundTotal() != null).map(HrBillDetailDTO::getAccumulationFundTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
                        hrBillReimbursementClient.setAccountId(hrClient.getProvidentFundAccountId());
                        break;
                }
                hrBillReimbursementClient.setAmount(bigDecimal);
                if (hrBillReimbursementClient.getAccountId() != null) {
                    HrPlatformAccount hrPlatformAccount = hrPlatformAccounts.stream().filter(ls -> ls.getId().equals(hrBillReimbursementClient.getAccountId())).findFirst().orElse(null);
                    if (hrPlatformAccount != null) {
                        hrBillReimbursementClient.setAccountNumber(hrPlatformAccount.getAccountNumber());
                    }
                }
                hrBillReimbursementClientDTOS.add(hrBillReimbursementClient);
                amount = amount.add(bigDecimal);
            }
            Map<String, List<HrBillReimbursementClientDTO>> listMap = hrBillReimbursementClientDTOS.stream().collect(Collectors.groupingBy(HrBillReimbursementClientDTO::getAccountNumber));
            List<HrBillReimbursementClientDTO> hrBillReimbursementClientDTOList = new ArrayList<>();
            for (String accountNumber : listMap.keySet()) {
                List<HrBillReimbursementClientDTO> reimbursementClients = listMap.get(accountNumber);
                List<String> clientId = reimbursementClients.stream().map(HrBillReimbursementClientDTO::getClientId).collect(Collectors.toList());
                BigDecimal reduce = reimbursementClients.stream().map(HrBillReimbursementClientDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                HrBillReimbursementClientDTO hrBillReimbursementClient = new HrBillReimbursementClientDTO();
                hrBillReimbursementClient
                    .setAccountNumber(accountNumber)
                    .setClientIds(clientId)
                    .setAmount(reduce);
                hrBillReimbursementClientDTOList.add(hrBillReimbursementClient);
            }
            List<String> clientIds = hrBillList.stream().map(HrBill::getClientId).distinct().collect(Collectors.toList());
            result.setClientNum(clientIds.size())
                .setHrBillReimbursementClientDTOS(hrBillReimbursementClientDTOS)
                .setHrBillReimbursementClientDTOList(hrBillReimbursementClientDTOList);
        } else {
            amount = hrBillReimbursementApplyDTO.getHrBillReimbursementClientDTOList().stream().map(HrBillReimbursementClientDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        //费用明细详情
        List<HrBillReimbursementApplyDetailDTO> detailDTOS = new ArrayList<>();
        detailDTOS.add(getApplyDetailDTO(hrBillReimbursementApplyDTO.getAccountType(), amount));
        result.setDetailDTOList(detailDTOS);
        result.setAmount(amount);
        return result;
    }

    /**
     * 已发起报销的结算单ID
     *
     * @param hrBillReimbursementApplyDTO
     * @return
     */
    private List<String> getFeeReviewId(HrBillReimbursementApplyDTO hrBillReimbursementApplyDTO) {
        List<String> feeReviewIds = new ArrayList<>();
        //自动化发起报销的结算单ID
        List<String> feeReviewId = hrBillReimbursementApplyRepository.selectBillId(hrBillReimbursementApplyDTO.getPayYear(), hrBillReimbursementApplyDTO.getPayMonth(), hrBillReimbursementApplyDTO.getAccountType());
        if (CollectionUtils.isNotEmpty(feeReviewId)) {
            feeReviewIds.addAll(feeReviewId);
        }
        //新建发起报销的结算单ID
        List<String> feeReviewIdList = hrBillReimbursementApplyRepository.selectClientApply(hrBillReimbursementApplyDTO.getPayYear(), hrBillReimbursementApplyDTO.getPayMonth(), hrBillReimbursementApplyDTO.getAccountType());
        if (CollectionUtils.isNotEmpty(feeReviewIdList)) {
            feeReviewIds.addAll(feeReviewIdList);
        }
        return feeReviewIds;
    }

    /**
     * 批量发起报销申请
     *
     * @param hrBillReimbursementApplyDTO
     * @return
     */
    @Override
    public HrBillReimbursementApplyDTO insertHrBillReimbursementApply(HrBillReimbursementApplyDTO hrBillReimbursementApplyDTO) {
        HrBillReimbursementApplyDTO hrBillReimbursementApply = this.createHrBillReimbursementApply(hrBillReimbursementApplyDTO);
        List<HrBillReimbursementClientDTO> hrBillReimbursementClientDTOS = hrBillReimbursementApplyDTO.getHrBillReimbursementClientDTOS();
        if (!hrBillReimbursementApplyDTO.getAccountType().equals(BillReimbApproveEnums.InvoiceTypeEnum.DF_SALARY.getKey())) {
            Map<String, List<HrBillReimbursementClientDTO>> listMap = hrBillReimbursementClientDTOS.stream().collect(Collectors.groupingBy(HrBillReimbursementClientDTO::getFeeReviewId));
            QueryWrapper<HrBillReimbursementApply> qw = new QueryWrapper<>();
            qw.in("bill_id", listMap.keySet());
            qw.eq("reimbursement_state", BillInvoiceApproveEnums.InvoiceState.OPENABLE_INVOICE.getKey());
            qw.eq("reimbursement_lock_state", BillInvoiceApproveEnums.InvoiceLockState.NOT_LOCKED.getKey());
            List<HrBillReimbursementApply> hrBillReimbursementApplies = hrBillReimbursementApplyRepository.selectList(qw);
            //根据结算单ID查询未锁定可开发票的报销明细，修改金额，扣除相对应的金额
            for (HrBillReimbursementApply apply : hrBillReimbursementApplies) {
                List<HrBillReimbursementApplyDetail> detailList = hrBillReimbursementApplyDetailRepository.selectList(new QueryWrapper<HrBillReimbursementApplyDetail>().eq("apply_id", apply.getId()));
                if (detailList != null && !detailList.isEmpty()) {
                    /*List<HrBillReimbursementClientDTO> reimbursementClientDTOList = listMap.get(apply.getBillId());
                    BigDecimal amount = reimbursementClientDTOList.stream().map(HrBillReimbursementClientDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal bigDecimal = CalculateUtils.decimalSubtraction(detail.getAmount(), amount);
                    String remark = "修改前金额为" + detail.getAmount() + "，修改后金额为" + bigDecimal;
                    detail.setRemark(remark).setAmount(bigDecimal).setLastModifiedDate(LocalDateTime.now());
                    hrBillReimbursementApplyDetailRepository.updateById(detail);
                    apply.setAmount(CalculateUtils.decimalSubtraction(apply.getAmount(), amount)).setLastModifiedDate(LocalDateTime.now());
                    hrBillReimbursementApplyRepository.updateById(apply);*/
                    detailList.forEach(ls -> {
                        if ((ls.getState().equals(BillInvoiceApproveEnums.InvoiceRecordState.NOT_LOCKED.getKey())
                            || ls.getState().equals(BillInvoiceApproveEnums.InvoiceRecordState.UNLOCK.getKey()))
                            && ls.getInvoiceType().equals(hrBillReimbursementApplyDTO.getAccountType())) {
                            ls.setState(BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey());
                            ls.setRemark("已通过汇总报销发起");
                            hrBillReimbursementApplyDetailRepository.updateById(ls);
                        }
                    });
                    List<HrBillReimbursementApplyDetail> notLockedRecordList = detailList.stream().filter(ls -> ls.getState().equals(BillInvoiceApproveEnums.InvoiceRecordState.NOT_LOCKED.getKey())
                        || ls.getState().equals(BillInvoiceApproveEnums.InvoiceRecordState.UNLOCK.getKey())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(notLockedRecordList)) {
                        hrBillReimbursementApplyDTO.setReimbursementLockState(BillInvoiceApproveEnums.InvoiceLockState.LOCKED.getKey());
                    } else {
                        hrBillReimbursementApplyDTO.setReimbursementLockState(BillInvoiceApproveEnums.InvoiceLockState.NOT_LOCKED.getKey());
                    }
                    hrBillReimbursementApplyRepository.updateLockState(apply.getId(), hrBillReimbursementApplyDTO.getReimbursementLockState());
                }
            }
            QueryWrapper<HrBillReimbursementApply> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("bill_id", listMap.keySet());
            queryWrapper.eq("reimbursement_state", BillInvoiceApproveEnums.InvoiceState.INVOICE_RECORD.getKey());
            queryWrapper.in("approve_status", 9, 10, 11);
            List<HrBillReimbursementApply> hrBillReimbursementApplyList = hrBillReimbursementApplyRepository.selectList(qw);
            for (HrBillReimbursementApply apply : hrBillReimbursementApplyList) {
                HrBillReimbursementApplyDetail applyDetail = hrBillReimbursementApplyDetailRepository.selectOne(new QueryWrapper<HrBillReimbursementApplyDetail>().eq("apply_id", apply.getId()).eq("invoice_type", hrBillReimbursementApplyDTO.getAccountType()).last("LIMIT 1"));
                if (applyDetail != null) {
                    hrBillReimbursementApplyDetailRepository.updateState(Collections.singletonList(applyDetail.getId()), BillInvoiceApproveEnums.InvoiceLockState.LOCKED.getKey());
                }
            }
        }
        for (HrBillReimbursementClientDTO hrBillReimbursementClientDTO : hrBillReimbursementClientDTOS) {
            hrBillReimbursementClientDTO.setApplyId(hrBillReimbursementApply.getId());
            HrBillReimbursementClient hrBillReimbursementClient = hrBillReimbursementClientMapper.toEntity(hrBillReimbursementClientDTO);
            hrBillReimbursementClientRepository.insert(hrBillReimbursementClient);
        }
        return hrBillReimbursementApply;
    }

    /**
     * 从对账自动生成报销单
     *
     * @param hrBillReimbursementApplyDTO
     * @return
     */
    @Override
    public HrBillReimbursementApplyDTO insertHrBillReimbursementApplyFromCompareResult(HrBillReimbursementApplyDTO hrBillReimbursementApplyDTO) {
        HrBillReimbursementApplyDTO hrBillReimbursementApply = this.createHrBillReimbursementApply(hrBillReimbursementApplyDTO);
        List<HrBillReimbursementClientDTO> hrBillReimbursementClientDTOS = hrBillReimbursementApplyDTO.getHrBillReimbursementClientDTOS().stream().peek(it -> it.setApplyId(hrBillReimbursementApply.getId())).collect(Collectors.toList());
        hrBillReimbursementClientService.saveBatch(hrBillReimbursementClientMapper.toEntity(hrBillReimbursementClientDTOS));
        return hrBillReimbursementApply;
    }

    /**
     * 代发工资符合账单查询
     *
     * @param hrBillReimbursementApplyDTO
     * @return
     */
    @Override
    public List<HrFeeReviewDTO> findSalaryBill(HrBillReimbursementApplyDTO hrBillReimbursementApplyDTO) {
        hrBillReimbursementApplyDTO.setAccountType(BillReimbApproveEnums.InvoiceTypeEnum.DF_SALARY.getKey());
        List<String> feeReviewIds = this.getFeeReviewId(hrBillReimbursementApplyDTO);
        List<HrFeeReviewDTO> hrFeeReviewDTOS = hrFeeReviewRepository.selectFeeReview(hrBillReimbursementApplyDTO.getPaymentDate(), feeReviewIds);
        if (CollectionUtils.isEmpty(hrFeeReviewDTOS)) {
            throw new CommonException("没有符合创建报销申请的结算单信息");
        }
        List<HrFeeReviewDTO> hrFeeReviewDTOList = new ArrayList<>();
        for (HrFeeReviewDTO hrFeeReviewDTO : hrFeeReviewDTOS) {
            List<String> billIds = Arrays.asList(hrFeeReviewDTO.getBillId().split(","));
            QueryWrapper<HrBill> qw = new QueryWrapper<>();
            qw.in("id", billIds);
            qw.eq("bill_type", BillEnum.BillType.SALARY_BILL.getKey());
            Integer count = hrBillRepository.selectCount(qw);
            if (count != 0) {
                HrBillTotal hrBillTotal = hrBillTotalRepository.selectOne(new QueryWrapper<HrBillTotal>().eq("is_delete", 0).eq("bill_id", hrFeeReviewDTO.getId())
                    .orderByDesc("created_date").last("LIMIT 1"));
                if (hrBillTotal != null) {
                    //代发工资 = 实发工资
                    hrFeeReviewDTO.setTotalAmount(hrBillTotal.getRealSalaryTotal());
                    hrFeeReviewDTOList.add(hrFeeReviewDTO);
                }
            }
        }
        return hrFeeReviewDTOList;
    }

    /**
     * 海尔客户查询可用账单汇总
     *
     * @param applyId 报销单ID
     * @return 账单汇总
     */
    @Override
    public List<HrBillTotalDTO> findHaierBill(String applyId) {
        HrBillReimbursementApply hrBillReimbursementApply = hrBillReimbursementApplyRepository.selectById(applyId);
        HrClient rootParentClient = hrClientRepository.getRootParentClient(hrBillReimbursementApply.getClientId());
        if (rootParentClient.getId().equals(SpecialBillClient.HAIER.getKey())) {
            HrFeeReview hrFeeReview = hrFeeReviewRepository.selectById(hrBillReimbursementApply.getBillId());
            List<String> billIds = Arrays.asList(hrFeeReview.getBillId());
            List<HrBillTotalDTO> hrBillTotalDTOS = hrBillTotalRepository.getBillTotalByBillId(billIds);
            for (HrBillTotalDTO billTotalDTO : hrBillTotalDTOS) {
                //(单位+个人)养老、(单位+个人)失业、(单位)工伤、(单位)补充工伤在申请报销时都将并入代缴社保；
                BigDecimal socialSecurityTotal = CalculateUtils.decimalListAddition(billTotalDTO.getUnitPensionTotal(),
                    billTotalDTO.getUnitUnemploymentTotal(),
                    billTotalDTO.getWorkInjuryTotal(),
                    billTotalDTO.getReplenishWorkInjuryExpenseTotal(),
                    billTotalDTO.getPersonalPensionTotal(),
                    billTotalDTO.getPersonalUnemploymentTotal());
                //(单位+个人)医疗、(单位)生育、(单位+个人)大额医疗在申请报销时都将并入代缴医保。
                BigDecimal medicalInsuranceTotal = CalculateUtils.decimalListAddition(billTotalDTO.getUnitMedicalTotal(),
                    billTotalDTO.getPersonalMedicalTotal(),
                    billTotalDTO.getUnitMaternityTotal(),
                    billTotalDTO.getUnitLargeMedicalExpenseTotal(),
                    billTotalDTO.getPersonalLargeMedicalExpenseTotal());
                billTotalDTO.setSocialSecurityTotal(socialSecurityTotal);
                billTotalDTO.setMedicalInsuranceTotal(medicalInsuranceTotal);
            }
            return hrBillTotalDTOS;
        }
        return new ArrayList<>();
    }

    /**
     * 生成凭证
     * <p>
     * 报销中的银行存款使用一条客商（其他）银行默认1067（可选）；
     * 凭证类别： 01记账凭证   制单日期: 默认当前日期
     *
     * @param hrBillReimbursementApplyDTO
     */
    @Override
    public void createVoucher(HrBillReimbursementApplyDTO hrBillReimbursementApplyDTO) {
        List<BankDepositDTO> bankDepositList = hrBillReimbursementApplyDTO.getBankDepositList();
        // 获取当前用户
        JWTUserDTO userDTO = SecurityUtils.getJwtUser();
        HrBillReimbursementApplyDTO hrBillReimbursementApply = this.hrBillReimbursementApplyRepository.getById(hrBillReimbursementApplyDTO.getId());
        if (!hrBillReimbursementApply.getApproveStatus().equals(BillReimbApproveEnums.SUCCESS.getKey())) {
            throw new CommonException("审批进度为审批通过，才能生成凭证！");
        }
        // 验证参数
        if (hrBillReimbursementApplyDTO.getReimburseType() == null) {
            throw new CommonException("请选择业务类型！");
        }
        if (StringUtils.isBlank(hrBillReimbursementApplyDTO.getAttachment())) {
            throw new CommonException("凭证的附件张数不能为空！");
        }
        if (StringUtils.isBlank(hrBillReimbursementApplyDTO.getPrepareddate())) {
            throw new CommonException("凭证的制单日期不能为空！");
        }
        if (UserRoleTypeEnum.ACCOUNTING.getKey().equals(userDTO.getCurrentRoleKey())) {
            // 验证会计用户的制单人编码是否已经填写
            UserDTO user = userRepository.getUserInFor(userDTO.getId());
            if (user == null) {
                throw new CommonException("用户不存在或者被禁用！");
            }
            if (StringUtils.isBlank(user.getBillmaker())) {
                throw new CommonException("用户的NC制单人编码尚未配置！");
            }
            VoucherHeadDTO voucherHead = new VoucherHeadDTO();
            voucherHead.setAccountbook(ncAccountbook);
            voucherHead.setVouchertype("01");
            voucherHead.setBillmaker(user.getBillmaker());
            voucherHead.setAttachment(hrBillReimbursementApplyDTO.getAttachment());
            voucherHead.setPrepareddate(hrBillReimbursementApplyDTO.getPrepareddate());
            voucherHead.setPeriod(hrBillReimbursementApplyDTO.getPayMonthly().toString());
            List<VoucherDetailDTO> voucherDetailList = new ArrayList<>();
            // 借金额
            BigDecimal jieAmount = BigDecimal.ZERO;
            // 贷金额
            BigDecimal daiAmount = BigDecimal.ZERO;
            // 获取费用明细
            HrBillReimbursementApplyDTO applyDTO = this.hrBillReimbursementApplyRepository.getById(hrBillReimbursementApplyDTO.getId());
            if (applyDTO == null) {
                throw new CommonException("报销申请的费用明细未查询到！");
            }
            List<HrBillReimbursementApplyDetailDTO> detailDTOS = this.findDetailByApplyId(hrBillReimbursementApplyDTO.getId(), hrBillReimbursementApply);
            if (detailDTOS == null || detailDTOS.isEmpty()) {
                throw new CommonException("报销申请不包含生成凭证的分录项！");
            }
            // 是否存在差旅费
            for (HrBillReimbursementApplyDetailDTO billReimbursementApplyDetail : detailDTOS) {
                if (applyDTO.getAccountType() == null) {
                    // 从结算单自动生成的 普通客户  clientId 使用applyDTO.getClientId()  使用2个表        差旅费 只支持这种
                    this.makeVoucherDetail(hrBillReimbursementApplyDTO.getReimburseType(), billReimbursementApplyDetail.getInvoiceTypeName(), applyDTO.getClientId(), billReimbursementApplyDetail.getAmount(), voucherDetailList, applyDTO.getPayMonth());
                    jieAmount = jieAmount.add(billReimbursementApplyDetail.getAmount());
                } else {
                    if (applyDTO.getAccountType().equals(BillReimbApproveEnums.InvoiceTypeEnum.SPECIAL_CLIENT.getKey())) {
                        // 如果是海尔那种特殊客户的话 使用hr_bill_reimbursement_client+hr_bill_reimbursement_detail_client  四个表
                        // detail里面取invoiceTypeName detail-> hrBillReimbursementDetailClientDTOList  里面取clientId+amount(realSalaryAmount，socialSecurityAmount，medicalInsuranceAmount，accumulationFoundAmount 其中之一)
                        List<HrBillReimbursementClientDTO> detailClientList = billReimbursementApplyDetail.getHrBillReimbursementDetailClientDTOList();
                        for (HrBillReimbursementClientDTO detailClient : detailClientList) {
                            BigDecimal amount = BigDecimal.ZERO;
                            if (billReimbursementApplyDetail.getInvoiceTypeName().equals("代发工资")) {
                                amount = detailClient.getRealSalaryAmount();
                            } else if (billReimbursementApplyDetail.getInvoiceTypeName().equals("代缴社保")) {
                                amount = detailClient.getSocialSecurityAmount();
                            } else if (billReimbursementApplyDetail.getInvoiceTypeName().equals("代缴医保")) {
                                amount = detailClient.getMedicalInsuranceAmount();
                            } else if (billReimbursementApplyDetail.getInvoiceTypeName().equals("代缴公积金")) {
                                amount = detailClient.getAccumulationFoundAmount();
                            }
                            this.makeVoucherDetail(hrBillReimbursementApplyDTO.getReimburseType(), billReimbursementApplyDetail.getInvoiceTypeName(), detailClient.getClientId(), amount, voucherDetailList, applyDTO.getPayMonth());
                            jieAmount = jieAmount.add(amount);
                        }
                    } else {
                        // 新建报销那种 account_type ！=null and account_type ！= 6   使用三个表 hrBillReimbursementClientDTOList 拿clientId+amount;
                        List<HrBillReimbursementClientDTO> billReimbursementClientDTOList = hrBillReimbursementApply.getHrBillReimbursementClientDTOList();
                        for (HrBillReimbursementClientDTO item : billReimbursementClientDTOList) {
                            this.makeVoucherDetail(hrBillReimbursementApplyDTO.getReimburseType(), billReimbursementApplyDetail.getInvoiceTypeName(), item.getClientId(), item.getAmount(), voucherDetailList, applyDTO.getPayMonth());
                            jieAmount = jieAmount.add(item.getAmount());
                        }
                    }
                }
            }

            // 如果为差旅费报销  需要增加税费
            if (hrBillReimbursementApplyDTO.getTaxAmount() != null && hrBillReimbursementApplyDTO.getTaxRate() != null) {
                List<NcAccountDTO> accounts3 = ncAccountRepository.findByTypeAndContentAndDebt(2, "差旅费报销税费", "D");
                if (accounts3 == null || accounts3.isEmpty()) {
                    throw new CommonException("差旅费税额的科目未查询到！");
                }
                NcAccountDTO ncAccount3 = accounts3.get(0);
                ncAccount3.setAmount(hrBillReimbursementApplyDTO.getTaxAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                ncAccount3.setTaxRate(hrBillReimbursementApplyDTO.getTaxRate());
                voucherDetailList.add(this.generateVoucherDetail(ncAccount3, null, applyDTO.getPayMonth()));
                jieAmount.add(hrBillReimbursementApplyDTO.getTaxAmount());
            }

            if (bankDepositList == null || bankDepositList.isEmpty()) {
                throw new CommonException("请填写银行存款的记录！");
            }
            List<NcAccountDTO> ncAccountList = ncAccountRepository.findByTypeAndContentAndDebt(2, "银行存款", "C");
            if (ncAccountList == null || ncAccountList.isEmpty()) {
                throw new CommonException("银行存款的科目未查询到！");
            }
            NcAccountDTO ncAccount = ncAccountList.get(0);
            for (BankDepositDTO bankDeposit : bankDepositList) {
                NcAccountDTO ncAccount4 = new NcAccountDTO();
                BeanUtils.copyProperties(ncAccount, ncAccount4);
                ncAccount.setAmount(bankDeposit.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                ncAccount.setAccountNumber(bankDeposit.getBankAccount());
                ncAccount.setExplanation(bankDeposit.getExplanation());
                voucherDetailList.add(this.generateVoucherDetail(ncAccount, null, applyDTO.getPayMonth()));
                daiAmount = daiAmount.add(bankDeposit.getAmount());
            }
            // 验证借贷是否相等
            if (jieAmount.compareTo(daiAmount) != 0) {
                throw new CommonException("借贷金额不相等！");
            }
            // 调用NC生成凭证接口
            log.info("报销记录 调用NC生成凭证接口，参数：voucherHead={},voucherDetailList={}", voucherHead, voucherDetailList);
            NcResultDTO voucherResult = ncService.createVoucher(voucherHead, voucherDetailList);
            log.info("报销记录 调用NC生成凭证接口，参数：voucherHead={},voucherDetailList={},返回值为{}", voucherHead, voucherDetailList, voucherResult);
            // 处理一下
            if (voucherResult.getErrorNo().equals("1")) {
                log.error("报销记录 调用NC生成凭证接口，参数：voucherHead={},voucherDetailList={},返回值为{}", voucherHead, voucherDetailList, voucherResult);
                throw new CommonException("调用NC系统生成凭证失败，原因：" + voucherResult.getMessage());
            }
            hrBillReimbursementApply.setNcVoucher(voucherResult.getVoucherNum());
            // 凭证状态改为已开
            hrBillReimbursementApply.setAccountingVoucherStatus(1);
            saveOrUpdate(this.hrBillReimbursementApplyMapper.toEntity(hrBillReimbursementApply));
        } else {
            throw new CommonException("只有会计才能生成凭证，您无操作权限");
        }
    }

    public void createVoucherNcc(HrBillReimbursementApplyDTO hrBillReimbursementApplyDTO) {
        List<BankDepositDTO> bankDepositList = hrBillReimbursementApplyDTO.getBankDepositList();
        // 获取当前用户
        JWTUserDTO userDTO = SecurityUtils.getJwtUser();
        HrBillReimbursementApplyDTO hrBillReimbursementApply = this.hrBillReimbursementApplyRepository.getById(hrBillReimbursementApplyDTO.getId());
        if (!hrBillReimbursementApply.getApproveStatus().equals(BillReimbApproveEnums.SUCCESS.getKey())) {
            throw new CommonException("审批进度为审批通过，才能生成凭证！");
        }
        if (StringUtils.isBlank(hrBillReimbursementApplyDTO.getAttachment())) {
            throw new CommonException("凭证的附件张数不能为空！");
        }
        if (StringUtils.isBlank(hrBillReimbursementApplyDTO.getPrepareddate())) {
            throw new CommonException("凭证的制单日期不能为空！");
        }
        if (UserRoleTypeEnum.ACCOUNTING.getKey().equals(userDTO.getCurrentRoleKey())) {
            // 验证会计用户的制单人编码是否已经填写
            UserDTO user = userRepository.getUserInFor(userDTO.getId());
            if (user == null) {
                throw new CommonException("用户不存在或者被禁用！");
            }
            if (StringUtils.isBlank(user.getBillmaker())) {
                throw new CommonException("用户的NC制单人编码尚未配置！");
            }
            VoucherHeadDTO voucherHead = new VoucherHeadDTO();
            voucherHead.setAccountbook(ncAccountbook);
            voucherHead.setVouchertype("01");
            voucherHead.setBillmaker(user.getBillmaker());
            voucherHead.setAttachment(hrBillReimbursementApplyDTO.getAttachment());
            voucherHead.setPrepareddate(hrBillReimbursementApplyDTO.getPrepareddate());
            voucherHead.setPeriod(hrBillReimbursementApplyDTO.getPayMonthly().toString());
            List<VoucherDetailDTO> voucherDetailList = new ArrayList<>();
            // 借金额
            BigDecimal jieAmount = BigDecimal.ZERO;
            // 贷金额
            BigDecimal daiAmount = BigDecimal.ZERO;
            // 获取费用明细
            HrBillReimbursementApplyDTO applyDTO = this.hrBillReimbursementApplyRepository.getById(hrBillReimbursementApplyDTO.getId());
            if (applyDTO == null) {
                throw new CommonException("报销申请的费用明细未查询到！");
            }
            List<HrBillReimbursementApplyDetailDTO> detailDTOS = this.findDetailByApplyId(hrBillReimbursementApplyDTO.getId(), hrBillReimbursementApply);
            if (detailDTOS == null || detailDTOS.isEmpty()) {
                throw new CommonException("报销申请不包含生成凭证的分录项！");
            }
            // 是否存在差旅费
            for (HrBillReimbursementApplyDetailDTO billReimbursementApplyDetail : detailDTOS) {
                if (applyDTO.getAccountType() == null) {
                    // 从结算单自动生成的 普通客户  clientId 使用applyDTO.getClientId()  使用2个表        差旅费 只支持这种
                    // Jigsaw - 2025/8/12 ：中石化差旅补助，体现在其他账单，生成凭证时需要手动修改摘要：将其他改为差旅费
                    this.makeVoucherDetailNcc(billReimbursementApplyDetail.getInvoiceTypeName(), applyDTO.getClientId(), billReimbursementApplyDetail.getAmount(), voucherDetailList, applyDTO.getPayMonth());
                    jieAmount = jieAmount.add(billReimbursementApplyDetail.getAmount());
                } else {
                    if (applyDTO.getAccountType().equals(BillReimbApproveEnums.InvoiceTypeEnum.SPECIAL_CLIENT.getKey())) {
                        // 如果是海尔那种特殊客户的话 使用hr_bill_reimbursement_client+hr_bill_reimbursement_detail_client  四个表
                        // detail里面取invoiceTypeName detail-> hrBillReimbursementDetailClientDTOList  里面取clientId+amount(realSalaryAmount，socialSecurityAmount，medicalInsuranceAmount，accumulationFoundAmount 其中之一)
                        List<HrBillReimbursementClientDTO> detailClientList = billReimbursementApplyDetail.getHrBillReimbursementDetailClientDTOList();
                        for (HrBillReimbursementClientDTO detailClient : detailClientList) {
                            BigDecimal amount = BigDecimal.ZERO;
                            if (billReimbursementApplyDetail.getInvoiceTypeName().equals("代发工资")) {
                                amount = detailClient.getRealSalaryAmount();
                            } else if (billReimbursementApplyDetail.getInvoiceTypeName().equals("代缴社保")) {
                                amount = detailClient.getSocialSecurityAmount();
                            } else if (billReimbursementApplyDetail.getInvoiceTypeName().equals("代缴医保")) {
                                amount = detailClient.getMedicalInsuranceAmount();
                            } else if (billReimbursementApplyDetail.getInvoiceTypeName().equals("代缴公积金")) {
                                amount = detailClient.getAccumulationFoundAmount();
                            }
                            this.makeVoucherDetailNcc(billReimbursementApplyDetail.getInvoiceTypeName(), detailClient.getClientId(), amount, voucherDetailList, applyDTO.getPayMonth());
                            jieAmount = jieAmount.add(amount);
                        }
                    } else {
                        // 新建报销那种 account_type ！=null and account_type ！= 6   使用三个表 hrBillReimbursementClientDTOList 拿clientId+amount;
                        List<HrBillReimbursementClientDTO> billReimbursementClientDTOList = hrBillReimbursementApply.getHrBillReimbursementClientDTOList();
                        for (HrBillReimbursementClientDTO item : billReimbursementClientDTOList) {
                            this.makeVoucherDetailNcc(billReimbursementApplyDetail.getInvoiceTypeName(), item.getClientId(), item.getAmount(), voucherDetailList, applyDTO.getPayMonth());
                            jieAmount = jieAmount.add(item.getAmount());
                        }
                    }
                }
            }

            if (bankDepositList == null || bankDepositList.isEmpty()) {
                throw new CommonException("请填写银行存款的记录！");
            }
            // 开票贷银行存款
            List<NcAccountDTO> ncAccountList = ncAccountRepository.findByTypeAndContentAndDebt(2, "银行存款", "C");
            if (ncAccountList == null || ncAccountList.isEmpty()) {
                throw new CommonException("银行存款的科目未查询到！");
            }
            NcAccountDTO ncAccount = ncAccountList.get(0);
            for (BankDepositDTO bankDeposit : bankDepositList) {
                NcAccountDTO ncAccount4 = new NcAccountDTO();
                BeanUtils.copyProperties(ncAccount, ncAccount4);
                ncAccount.setAmount(bankDeposit.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                ncAccount.setAccountNumber(bankDeposit.getBankAccount());
                ncAccount.setExplanation(bankDeposit.getExplanation());
                voucherDetailList.add(this.generateVoucherDetail(ncAccount, null, applyDTO.getPayMonth()));
                daiAmount = daiAmount.add(bankDeposit.getAmount());
            }
            // 验证借贷是否相等
            if (jieAmount.compareTo(daiAmount) != 0) {
                throw new CommonException("借贷金额不相等！");
            }
            // 调用NC生成凭证接口
            log.info("报销记录 调用NC生成凭证接口，参数：voucherHead={},voucherDetailList={}", voucherHead, voucherDetailList);
            NcResultDTO voucherResult = ncService.createVoucher(voucherHead, voucherDetailList);
            log.info("报销记录 调用NC生成凭证接口，参数：voucherHead={},voucherDetailList={},返回值为{}", voucherHead, voucherDetailList, voucherResult);
            // 处理一下
            if (voucherResult == null || voucherResult.getErrorNo().equals("1")) {
                log.error("报销记录 调用NC生成凭证接口，参数：voucherHead={},voucherDetailList={},返回值为{}", voucherHead, voucherDetailList, voucherResult);
                throw new CommonException("调用NC系统生成凭证失败，原因：" + (voucherResult == null ? "返回值为空" : voucherResult.getMessage()));
            }
            hrBillReimbursementApply.setNcVoucher(voucherResult.getVoucherNum());
            // 凭证状态改为已开
            hrBillReimbursementApply.setAccountingVoucherStatus(1);
            saveOrUpdate(this.hrBillReimbursementApplyMapper.toEntity(hrBillReimbursementApply));
        } else {
            throw new CommonException("只有会计才能生成凭证，您无操作权限");
        }
    }

    /**
     * 根据申请id查询银行存款记录
     *
     * @param applyId
     * @return
     */
    @Override
    public List<BankDepositDTO> getBankDepositList(String applyId) {
        List<BankDepositDTO> bankDepositList = new ArrayList<>();

        HrBillReimbursementApply hrBillReimbursementApply = this.getById(applyId);
        if (hrBillReimbursementApply == null) {
            throw new CommonException("报销申请不存在！");
        }
        // todo 查询客户银行卡号,clientId,bankValue
        Map<String, String> clientBankMap = hrClientRepository.getClientPayrollNumberList().stream().collect(Collectors.toMap(HrClient::getId, HrClient::getPayrollAccountNumber));

        HrBillReimbursementApplyDTO applyDTO = this.hrBillReimbursementApplyMapper.toDto(hrBillReimbursementApply);
        // 获取报销申请详情，代发工资/社保/医保/公积金
        List<HrBillReimbursementApplyDetailDTO> detailDTOS = this.hrBillReimbursementApplyDetailRepository.getByApplyId(applyId);
        if (hrBillReimbursementApply.getAccountType() != null) {
            // 手动申请报销
            if (applyDTO.getAccountType().equals(BillReimbApproveEnums.InvoiceTypeEnum.SPECIAL_CLIENT.getKey())) {
                // 自动化流转过去的，但是报销申请时只使用代发工资
                List<HrBillReimbursementClientDTO> hrBillReimbursementClientDTOList = hrBillReimbursementApplyRepository.findSpecialClientInfo(applyId);
                applyDTO.setHrBillReimbursementClientDTOList(hrBillReimbursementClientDTOList);
                for (HrBillReimbursementApplyDetailDTO detailDTO : detailDTOS) {
                    List<HrBillReimbursementClientDTO> hrBillReimbursementDetailClientDTOList = hrBillReimbursementApplyDetailRepository.getDetailClientById(Collections.singletonList(detailDTO.getId()), null);
                    detailDTO.setHrBillReimbursementDetailClientDTOList(hrBillReimbursementDetailClientDTOList);
                }
            } else {
                List<HrBillReimbursementClientDTO> hrBillReimbursementClientDTOList = hrBillReimbursementApplyRepository.findBillByApplyId(applyId);
                applyDTO.setHrBillReimbursementClientDTOList(hrBillReimbursementClientDTOList);
            }
        }

        for (HrBillReimbursementApplyDetailDTO billReimbursementApplyDetail : detailDTOS) {
            if (applyDTO.getAccountType() == null) {
                // 从结算单自动生成的 普通客户  clientId 使用applyDTO.getClientId()  使用2个表        差旅费 只支持这种
//                this.makeVoucherDetailNcc(billReimbursementApplyDetail.getInvoiceTypeName(), applyDTO.getClientId(), billReimbursementApplyDetail.getAmount(), voucherDetailList, applyDTO.getPayMonth());
//                jieAmount = jieAmount.add(billReimbursementApplyDetail.getAmount());
                bankDepositList.add(new BankDepositDTO(clientBankMap.getOrDefault(applyDTO.getClientId(), Constants.PAYROLL_ACCOUNT_NUMBER_DEFAULT), billReimbursementApplyDetail.getAmount(), billReimbursementApplyDetail.getInvoiceTypeName()));
            } else {
                // 手动新增的只有一条详情
                if (applyDTO.getAccountType().equals(BillReimbApproveEnums.InvoiceTypeEnum.SPECIAL_CLIENT.getKey())) {
                    // 如果是海尔那种特殊客户的话 使用hr_bill_reimbursement_client+hr_bill_reimbursement_detail_client  四个表
                    // detail里面取invoiceTypeName detail-> hrBillReimbursementDetailClientDTOList  里面取clientId+amount(realSalaryAmount，socialSecurityAmount，medicalInsuranceAmount，accumulationFoundAmount 其中之一)
                    List<HrBillReimbursementClientDTO> detailClientList = billReimbursementApplyDetail.getHrBillReimbursementDetailClientDTOList();
                    for (HrBillReimbursementClientDTO detailClient : detailClientList) {
                        BigDecimal amount = BigDecimal.ZERO;
                        if (billReimbursementApplyDetail.getInvoiceTypeName().equals("代发工资")) {
                            amount = detailClient.getRealSalaryAmount();
                        } else if (billReimbursementApplyDetail.getInvoiceTypeName().equals("代缴社保")) {
                            amount = detailClient.getSocialSecurityAmount();
                        } else if (billReimbursementApplyDetail.getInvoiceTypeName().equals("代缴医保")) {
                            amount = detailClient.getMedicalInsuranceAmount();
                        } else if (billReimbursementApplyDetail.getInvoiceTypeName().equals("代缴公积金")) {
                            amount = detailClient.getAccumulationFoundAmount();
                        }
                        bankDepositList.add(new BankDepositDTO(clientBankMap.getOrDefault(detailClient.getClientId(), Constants.PAYROLL_ACCOUNT_NUMBER_DEFAULT), amount, billReimbursementApplyDetail.getInvoiceTypeName()));
//                        this.makeVoucherDetailNcc(billReimbursementApplyDetail.getInvoiceTypeName(), detailClient.getClientId(), amount, voucherDetailList, applyDTO.getPayMonth());
//                        jieAmount = jieAmount.add(amount);
                    }
                } else {
                    // 新建报销那种 account_type ！=null and account_type ！= 6   使用三个表 hrBillReimbursementClientDTOList 拿clientId+amount;
                    List<HrBillReimbursementClientDTO> billReimbursementClientDTOList = applyDTO.getHrBillReimbursementClientDTOList();
                    for (HrBillReimbursementClientDTO item : billReimbursementClientDTOList) {
                        bankDepositList.add(new BankDepositDTO(clientBankMap.getOrDefault(item.getClientId(), Constants.PAYROLL_ACCOUNT_NUMBER_DEFAULT), item.getAmount(), billReimbursementApplyDetail.getInvoiceTypeName()));
//                        this.makeVoucherDetailNcc(billReimbursementApplyDetail.getInvoiceTypeName(), item.getClientId(), item.getAmount(), voucherDetailList, applyDTO.getPayMonth());
//                        jieAmount = jieAmount.add(item.getAmount());
                    }
                }
            }
        }
        return bankDepositList;
    }
    /**
     * 生成凭证  查询出  客户+报销内容+金额
     *
     * @param id
     * @param applyDTO
     * @return
     */
    private List<HrBillReimbursementApplyDetailDTO> findDetailByApplyId(String id, HrBillReimbursementApplyDTO applyDTO) {
        List<HrBillReimbursementApplyDetailDTO> detailDTOS = this.hrBillReimbursementApplyDetailRepository.getByApplyId(id);
        if (applyDTO.getAccountType() != null) {
            if (applyDTO.getAccountType().equals(BillReimbApproveEnums.InvoiceTypeEnum.SPECIAL_CLIENT.getKey())) {
                List<HrBillReimbursementClientDTO> hrBillReimbursementClientDTOList = hrBillReimbursementApplyRepository.findSpecialClientInfo(id);
                applyDTO.setHrBillReimbursementClientDTOList(hrBillReimbursementClientDTOList);
                for (HrBillReimbursementApplyDetailDTO detailDTO : detailDTOS) {
                    List<HrBillReimbursementClientDTO> hrBillReimbursementDetailClientDTOList = hrBillReimbursementApplyDetailRepository.getDetailClientById(Collections.singletonList(detailDTO.getId()), null);
                    detailDTO.setHrBillReimbursementDetailClientDTOList(hrBillReimbursementDetailClientDTOList);
                }
            } else {
                List<HrBillReimbursementClientDTO> hrBillReimbursementClientDTOList = hrBillReimbursementApplyRepository.findBillByApplyId(id);
                applyDTO.setHrBillReimbursementClientDTOList(hrBillReimbursementClientDTOList);
            }
        }
        return detailDTOS;
    }

    /**
     * 生成凭证需要的分录项
     * NC科目类型：1发票、2报销、3派遣报销、4外包报销
     *
     * @param reimburseType 业务类型：1派遣  2外包  3其他
     * @param content       报销内容：例如代开工资等
     * @param clientId      平台的客户ID
     * @param amount        金额
     */
    private void makeVoucherDetail(Integer reimburseType, String content, String clientId, BigDecimal amount, List<VoucherDetailDTO> voucherDetailList, Integer payMonth) {
        // 如果金额为0，忽略
        if (amount.compareTo(BigDecimal.ZERO) == 1) {
            NcCustomerDTO ncCustomer = ncCustomerRepository.findByClientId(clientId);
            if (ncCustomer == null) {
                log.info("客户与NC客户的关联未设置,clientId={}", clientId);
                throw new CommonException("客户与NC客户的关联未设置！");
            }
            // 如果类型为外包、派遣
            if (reimburseType == 1 || reimburseType == 2) {
                List<NcAccountDTO> accounts = ncAccountRepository.findByTypeAndContentAndDebt(reimburseType.equals(1) ? 3 : 4, content, "D");
                if (accounts == null || accounts.isEmpty()) {
                    throw new CommonException("生成凭证不支持报销内容：" + content + "！");
                }
                NcAccountDTO ncAccount1 = accounts.get(0);
                ncAccount1.setAmount(amount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                voucherDetailList.add(this.generateVoucherDetail(ncAccount1, ncCustomer, payMonth));
            } else {
                // 如果类型为其他    差旅费
                List<NcAccountDTO> accounts2 = ncAccountRepository.findByTypeAndContentAndDebt(2, content, "D");
                if (accounts2 == null || accounts2.isEmpty()) {
                    throw new CommonException("生成凭证不支持报销内容：" + content + "！");
                }
                NcAccountDTO ncAccount2 = accounts2.get(0);
                ncAccount2.setAmount(amount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                voucherDetailList.add(this.generateVoucherDetail(ncAccount2, ncCustomer, payMonth));
            }
        }
    }

    private void makeVoucherDetailNcc(String content, String clientId, BigDecimal amount, List<VoucherDetailDTO> voucherDetailList, Integer payMonth) {
        // 如果金额为0，忽略
        if (amount.compareTo(BigDecimal.ZERO) > 0) {
            NcCustomerDTO ncCustomer = ncCustomerRepository.findByClientId(clientId);
            if (ncCustomer == null) {
                log.info("客户与NC客户的关联未设置,clientId={}", clientId);
                throw new CommonException("客户与NC客户的关联未设置！");
            }
            // 不分派遣和外包
            List<NcAccountDTO> accounts = ncAccountRepository.findByTypeAndContentAndDebt(2, content, "D");
            if (accounts == null || accounts.isEmpty()) {
                throw new CommonException("生成凭证不支持报销内容：" + content + "！");
            }
            NcAccountDTO ncAccount1 = accounts.get(0);
            ncAccount1.setAmount(amount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            voucherDetailList.add(this.generateVoucherDetailNcc(ncAccount1, ncCustomer, payMonth));
        }
    }

    private VoucherDetailDTO generateVoucherDetailNcc(NcAccountDTO accountDTO, NcCustomerDTO ncCustomer, Integer payMonth) {
        VoucherDetailDTO voucherDetail = new VoucherDetailDTO();
        voucherDetail.setDebt(accountDTO.getDebt());
        voucherDetail.setAccasoacode(accountDTO.getAccountcode());
        String explanation = "";
        if (StringUtils.isNotBlank(accountDTO.getExplanation())) {
            explanation = accountDTO.getExplanation();
        } else {
            if (accountDTO.getContent().equals("代缴医保")) {
                // 代缴医保时：代缴 客商简称-医保-月份
                explanation = "代缴" + (StringUtils.isNotBlank(ncCustomer.getShortName()) ? ncCustomer.getShortName() : ncCustomer.getClientName()) + "-医保-" + payMonth + "月份";
            } else if (accountDTO.getContent().equals("代缴社保")) {
                // 代缴社保时：代缴 客商简称-社保-月份
                explanation = "代缴" + (StringUtils.isNotBlank(ncCustomer.getShortName()) ? ncCustomer.getShortName() : ncCustomer.getClientName()) + "-社保-" + payMonth + "月份";
            } else if (accountDTO.getContent().equals("代缴公积金")) {
                // 代缴公积金时：代缴 客商简称-公积金-月份
                explanation = "代缴" + (StringUtils.isNotBlank(ncCustomer.getShortName()) ? ncCustomer.getShortName() : ncCustomer.getClientName()) + "-公积金-" + payMonth + "月份";
            } else if (accountDTO.getContent().equals("差旅费报销")) {
                // 差旅费报销时：报销 客户简称 差旅费;
                explanation = "报销" + (StringUtils.isNotBlank(ncCustomer.getShortName()) ? ncCustomer.getShortName() : ncCustomer.getClientName()) + "差旅费";
            } else if (accountDTO.getContent().equals("代发工资")) {
                // 代发工资时 代发 客户简称 工资
                explanation = "代发" + (StringUtils.isNotBlank(ncCustomer.getShortName()) ? ncCustomer.getShortName() : ncCustomer.getClientName()) + "工资";
            }
        }
        voucherDetail.setExplanation(explanation);
        voucherDetail.setCurrency("CNY");
        voucherDetail.setAmount(accountDTO.getAmount());
        voucherDetail.setLocalexcrate("1.00");
        voucherDetail.setLocalamount(accountDTO.getAmount());
        voucherDetail.setGroupexcrate("1.00");
        voucherDetail.setGroupamount(accountDTO.getAmount());
        List<AuxiliaryDTO> auxiliarys = new ArrayList<>();
        List<AuxiliaryResultDTO> auxiliaryResultDTOS = JSONArray.parseArray(accountDTO.getAuxiliary(), AuxiliaryResultDTO.class);
        for (AuxiliaryResultDTO auxiliaryResult : auxiliaryResultDTOS) {
            AuxiliaryDTO auxiliaryDTO = new AuxiliaryDTO();
            // 0004客商  如果是银行存款  默认为其他
            if (auxiliaryResult.getAccasscode().equals("0004")) {
                auxiliaryDTO.setItemcode(auxiliaryResult.getAccasscode());
                auxiliaryDTO.setContentcode(ncCustomer == null ? "*********" : ncCustomer.getNcCode());
            }
            //  999项目(自定义档案)  默认其他
            if (auxiliaryResult.getAccasscode().equals("999")) {
                auxiliaryDTO.setItemcode(auxiliaryResult.getAccasscode());
                auxiliaryDTO.setContentcode("17");
            }
            //  004税率
            if (auxiliaryResult.getAccasscode().equals("004")) {
                auxiliaryDTO.setItemcode(auxiliaryResult.getAccasscode());
                auxiliaryDTO.setContentcode(NcTaxRateEnum.getValueByKey(accountDTO.getTaxRate()));
            }
            //  0007现金流量项目  默认 支付其他与经营活动有关的现金
            if (auxiliaryResult.getAccasscode().equals("0007")) {
                auxiliaryDTO.setItemcode(auxiliaryResult.getAccasscode());
                auxiliaryDTO.setContentcode("1124");
            }
            // 0011银行账户
            if (auxiliaryResult.getAccasscode().equals("0011")) {
                auxiliaryDTO.setItemcode(auxiliaryResult.getAccasscode());
                auxiliaryDTO.setContentcode(accountDTO.getAccountNumber());
            }
            auxiliarys.add(auxiliaryDTO);
        }
        voucherDetail.setAuxiliarys(auxiliarys);
        return voucherDetail;
    }

    /**
     * 报销中的银行存款使用一条客商（其他）银行默认1067（可选）；
     * 凭证类别： 01记账凭证   制单日期: 默认当前日期
     * contentcode对应对的是辅助核算具体的内容，比如客商编码，
     * itemcode对应的是辅助核算的类型，比如项目999，
     */
    private VoucherDetailDTO generateVoucherDetail(NcAccountDTO accountDTO, NcCustomerDTO ncCustomer, Integer payMonth) {
        VoucherDetailDTO voucherDetail = new VoucherDetailDTO();
        voucherDetail.setDebt(accountDTO.getDebt());
        voucherDetail.setAccasoacode(accountDTO.getAccountcode());
        String explanation = "";
        if (StringUtils.isNotBlank(accountDTO.getExplanation())) {
            explanation = accountDTO.getExplanation();
        } else {
            if (accountDTO.getContent().equals("代缴医保")) {
                // 代缴医保时：代缴 客商简称-医保-月份
                explanation = "代缴" + (StringUtils.isNotBlank(ncCustomer.getShortName()) ? ncCustomer.getShortName() : ncCustomer.getClientName()) + "-医保-" + payMonth + "月份";
            } else if (accountDTO.getContent().equals("代缴社保")) {
                // 代缴社保时：代缴 客商简称-社保-月份
                explanation = "代缴" + (StringUtils.isNotBlank(ncCustomer.getShortName()) ? ncCustomer.getShortName() : ncCustomer.getClientName()) + "-社保-" + payMonth + "月份";
            } else if (accountDTO.getContent().equals("代缴公积金")) {
                // 代缴公积金时：代缴 客商简称-公积金-月份
                explanation = "代缴" + (StringUtils.isNotBlank(ncCustomer.getShortName()) ? ncCustomer.getShortName() : ncCustomer.getClientName()) + "-公积金-" + payMonth + "月份";
            } else if (accountDTO.getContent().equals("差旅费报销")) {
                // 差旅费报销时：报销 客户简称 差旅费;
                explanation = "报销" + (StringUtils.isNotBlank(ncCustomer.getShortName()) ? ncCustomer.getShortName() : ncCustomer.getClientName()) + "差旅费";
            } else if (accountDTO.getContent().equals("代发工资")) {
                // 代发工资时 代发 客户简称 工资
                explanation = "代发" + (StringUtils.isNotBlank(ncCustomer.getShortName()) ? ncCustomer.getShortName() : ncCustomer.getClientName()) + "工资";
            }
        }
        voucherDetail.setExplanation(explanation);
        voucherDetail.setCurrency("CNY");
        voucherDetail.setAmount(accountDTO.getAmount());
        voucherDetail.setLocalexcrate("1.00");
        voucherDetail.setLocalamount(accountDTO.getAmount());
        voucherDetail.setGroupexcrate("1.00");
        voucherDetail.setGroupamount(accountDTO.getAmount());
        List<AuxiliaryDTO> auxiliarys = new ArrayList<>();
        List<AuxiliaryResultDTO> auxiliaryResultDTOS = JSONArray.parseArray(accountDTO.getAuxiliary(), AuxiliaryResultDTO.class);
        for (AuxiliaryResultDTO auxiliaryResult : auxiliaryResultDTOS) {
            AuxiliaryDTO auxiliaryDTO = new AuxiliaryDTO();
            // 0004客商  如果是银行存款  默认为其他
            if (auxiliaryResult.getAccasscode().equals("0004")) {
                auxiliaryDTO.setItemcode(auxiliaryResult.getAccasscode());
                auxiliaryDTO.setContentcode(ncCustomer == null ? "*********" : ncCustomer.getNcCode());
            }
            //  999项目(自定义档案)  默认其他
            if (auxiliaryResult.getAccasscode().equals("999")) {
                auxiliaryDTO.setItemcode(auxiliaryResult.getAccasscode());
                auxiliaryDTO.setContentcode("17");
            }
            //  004税率
            if (auxiliaryResult.getAccasscode().equals("004")) {
                auxiliaryDTO.setItemcode(auxiliaryResult.getAccasscode());
                auxiliaryDTO.setContentcode(NcTaxRateEnum.getValueByKey(accountDTO.getTaxRate()));
            }
            //  0007现金流量项目  默认 支付其他与经营活动有关的现金
            if (auxiliaryResult.getAccasscode().equals("0007")) {
                auxiliaryDTO.setItemcode(auxiliaryResult.getAccasscode());
                auxiliaryDTO.setContentcode("1124");
            }
            // 0011银行账户
            if (auxiliaryResult.getAccasscode().equals("0011")) {
                auxiliaryDTO.setItemcode(auxiliaryResult.getAccasscode());
                auxiliaryDTO.setContentcode(accountDTO.getAccountNumber());
            }
            auxiliarys.add(auxiliaryDTO);
        }
        voucherDetail.setAuxiliarys(auxiliarys);
        return voucherDetail;
    }

    /**
     * 删除凭证
     *
     * @param hrBillReimbursementApplyId
     */
    @Override
    public void deleteVoucher(String hrBillReimbursementApplyId) {
        this.hrBillReimbursementApplyRepository.deleteVoucher(hrBillReimbursementApplyId);
    }

    /**
     * 海尔报销:获取代发工资明细
     *
     * @param billTotalDTO 结算单汇总
     * @param result       报销单
     * @return
     */
    @Override
    public List<HrBillReimbursementApplyDetailDTO> assignmentSalaryDetailList(HrBillTotalDTO billTotalDTO, HrBillReimbursementApplyDTO result) {
        List<HrBillReimbursementApplyDetailDTO> detailDTOS = new ArrayList<>();
        BigDecimal realSalaryTotal = billTotalDTO.getRealSalaryTotal();
        detailDTOS.add(getApplyDetailDTO(BillReimbApproveEnums.InvoiceTypeEnum.DF_SALARY.getKey(), realSalaryTotal));
        result.setAmount(realSalaryTotal);
        return detailDTOS;
    }

    /**
     * 删除未使用的报销申请
     *
     * @param billCompareResultIds
     */
    @Override
    public void deleteByBillCompareResultId(List<String> billCompareResultIds) {
        if (CollectionUtils.isEmpty(billCompareResultIds)) {
            return;
        }
        LambdaQueryWrapper<HrBillReimbursementApply> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(HrBillReimbursementApply::getBillCompareResultId, billCompareResultIds);
        queryWrapper.eq(HrBillReimbursementApply::getReimbursementState, BillInvoiceApproveEnums.InvoiceState.OPENABLE_INVOICE.getKey());
        queryWrapper.eq(HrBillReimbursementApply::getApproveStatus, BillReimbApproveEnums.NOT_LAUNCH.getKey());
        this.remove(queryWrapper);
    }
}
