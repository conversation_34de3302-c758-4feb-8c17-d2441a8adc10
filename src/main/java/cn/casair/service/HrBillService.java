package cn.casair.service;

import cn.casair.common.enums.SpecialBillClient;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.dto.billl.BillExcelDataDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 账单服务类
 *
 * <AUTHOR>
 * @since 2021-10-26
 */
public interface HrBillService extends IService<HrBill> {

    /**
     * 下载账单明细
     *
     * @param params
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022/1/25
     **/
    String downloadBillDetail(Map<String, Object> params);

    /**
     * 账单批量下载
     *
     * @param hrBillDTO
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022/1/25
     **/
    String downloadBillBatch(HrBillDTO hrBillDTO, HttpServletResponse response);

    /**
     * 获取正常薪金最近有效的缴费年月
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/11/9
     **/
    String getNormalSalaryEffectivePaymentDate();

    /**
     * 正常薪金导出
     *
     * @param hrBillDTO
     * @param response
     * @return void
     * <AUTHOR>
     * @date 2021/11/7
     **/
    String normalSalaryExport(HrBillDTO hrBillDTO, HttpServletResponse response);

    /**
     * 正常薪金分页列表
     *
     * @param hrBillDTO
     * @param pageNumber
     * @param pageSize
     * @return com.baomidou.mybatisplus.core.metadata.IPage<cn.casair.dto.HrBillDTO>
     * <AUTHOR>
     * @date 2021/11/3
     **/
    IPage<HrBilNormalDTO> findNormalSalaryPage(HrBillDTO hrBillDTO, Long pageNumber, Long pageSize);

    /**
     * 复制账单
     *
     * @param hrBillDTO
     * @return
     */
    HrBillDTO copyHrBill(HrBillDTO hrBillDTO);

    /**
     * 创建账单
     *
     * @param hrBillDTO
     * @return
     */
    HrBillDTO createHrBill(HrBillDTO hrBillDTO);

    /**
     * 修改账单
     *
     * @param hrBillDTO
     * @return
     */
    void updateHrBill(HrBillDTO hrBillDTO);

    /**
     * 查询账单详情
     *
     * @param id
     * @return
     */
    HrBillDTO getHrBill(String id);

    /**
     * 删除账单
     *
     * @param id
     */
    void deleteHrBill(String id);

    /**
     * 批量删除账单
     *
     * @param ids
     */
    void deleteHrBill(List<String> ids);

    /**
     * 分页查询账单
     *
     * @param hrBillDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage<HrBillDTO> findPage(HrBillDTO hrBillDTO, Long pageNumber, Long pageSize);

    /**
     * 账单创建检查
     *
     * @param hrBillDTO
     * @return cn.casair.dto.BillCheckResultDTO
     * <AUTHOR>
     * @date 2021/11/4
     **/
    Map<String, Object> createHrBillCheck(HrBillDTO hrBillDTO);

    /**
     * 未入账员工明细
     *
     * @param params
     * @return
     */
    List<HrTalentStaffDTO> unrecordedDetails(Map<String, Object> params);

    /**
     * 创建账单基础信息
     *
     * @param hrBillDTO
     */
    HrBillDTO createBasicBill(HrBillDTO hrBillDTO);

    /**
     * 填充账单明细部分参数,主要是社保公积金等
     *
     * @param billDetail        账单明细
     * @param hrBill            账单基础信息
     * @param staffIds          确认入账员工ID
     * @param billDetailByBill 同月份保障账单
     * @param hrBillDetailDTOS 同月份薪酬账单
     * @param welfareCompensations
     * @return
     */
    HrBillDetailDTO fillHrBillDetail(HrBillDetailDTO billDetail, HrBillDTO hrBill, List<String> staffIds, HrSocialSecurityDTO socialSecurityDTO, List<HrBillCompareResultDTO> hrBillCompareResultDTOList,
                                     List<HrBillDetailDTO> billDetailByBill, List<HrBillDetailDTO> hrBillDetailDTOS, List<HrWelfareCompensation> welfareCompensations, int lastPayYear, int lastPayMonthly);

    /**
     * 初始化服务费
     *
     * @param hrBill        账单基础
     * @param billDetailDTO 员工账单信息
     * @param clientType
     * @return
     */
    void initServiceFee(HrBillDTO hrBill, HrBillDetailDTO billDetailDTO, Integer clientType);

    /**
     * 增加账单明细表与补差明细表的关系
     *
     * @param billDetail              账单明细
     * @param welfareCompensationList
     */
    void addMakeUpUserRecord(HrBillDetail billDetail, List<HrWelfareCompensation> welfareCompensationList);


    /**
     * 增加账单明细表与补差明细表的关系 从redis中取值
     *
     * @param billDetail 账单明细
     */
    void addMakeUpUserRecordFromRedis(HrBillDetail billDetail);

    /**
     * 增加账单明细表与补差明细表的关系 缓存到redis中
     *
     * @param billDetail              账单明细
     * @param welfareCompensationList
     */
    void addMakeUpUserRecordToRedis(HrBillDetail billDetail, List<HrWelfareCompensation> welfareCompensationList);

    ResponseEntity<?> getPromptLanguage(HrBillDTO hrBillDTO);

    /**
     * 工资发放分页列表
     *
     * @param hrBillDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage<HrSalaryPaymentDTO> findSalaryPaymentPage(HrBillDTO hrBillDTO, Long pageNumber, Long pageSize);

    /**
     * 工资发放导出银行报盘文件
     *
     * @param hrBillDTO
     * @param response
     * @return
     */
    String salaryPaymentBankReportExport(HrBillDTO hrBillDTO, HttpServletResponse response);

    /**
     * 工资发放导出
     *
     * @param hrBillDTO
     * @param response
     * @return
     */
    String exportDataList(HrBillDTO hrBillDTO, HttpServletResponse response);

    /**
     * 查询重复表单数据
     *
     * @param hrBillDTO 账单
     * @return
     */
    ResponseEntity<?> queryDuplicateFormData(HrBillDTO hrBillDTO);

    /**
     * 全年一次性奖金计税
     *
     * @param hrBillDTO
     * @return
     */
    List<HrBillDetailDTO> annualLumpSumBonusTax(HrBillDTO hrBillDTO);

    /**
     * 全年一次性奖金分页列表
     *
     * @param hrBillDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    IPage<HrAnnualBonusDTO> findAnnualLumpSumBonusPage(HrBillDTO hrBillDTO, Long pageNumber, Long pageSize);

    /**
     * 全年一次性奖金导出
     *
     * @param hrBillDTO
     * @return
     */
    String annualLumpSumBonusExportCheck(HrBillDTO hrBillDTO);

    /**
     * 查询多个账单详情
     *
     * @param billIdList 账单ID
     * @return 账单信息+账单明细列表+汇总账单
     */
    HrBillDTO getHrBillBatch(List<String> billIdList);

    /**
     * 暂时创建不可用账单
     *
     * @param usedClientIdList 可以创建账单的客户
     * @param hrBillDTO
     * @return
     */
    HrBill saveBillNotUsed(List<String> usedClientIdList, HrBillDTO hrBillDTO);

    /**
     * 从保障账单中获取社保数据
     *
     * @param billDetail
     * @param billDetailByBill 同月份保障账单
     * @param hrBillDetailDTOList 同月份薪酬账单
     */
    void getDataBySecurityBill(HrBillDetailDTO billDetail, List<HrBillDetailDTO> billDetailByBill, List<HrBillDetailDTO> hrBillDetailDTOList, List<HrWelfareCompensation> welfareCompensations, List<HrBillCompareResultDTO> hrBillCompareResultDTOList, int lastPayYear, int lastPayMonthly);

    /**
     * 特殊处理客户
     * 上月入职且有当月补拨员工
     * 薪酬账填充应发工资,税前应发,个税,实发工资,费用合计,免税收入等
     *
     * @param detailDTO
     * @param dynamicFeeItems
     * @param hrSecurityBillDetailDTO 保障账单
     */
    void fillSpecialSalaryParamForNewJoined(HrBillDetailDTO detailDTO, List<HrBillDetailItemsDTO> dynamicFeeItems, HrBillDetailDTO hrSecurityBillDetailDTO, Integer personalTaxStart,
                                            BigDecimal sumCurrentIncome, List<HrSpecialDeduction> specialDeductionList, List<HrQuickDeduction> hrQuickDeductions);

    /**
     * 特殊处理客户
     * 上月离职员工
     * 薪酬账填充应发工资,税前应发,个税,实发工资,费用合计,免税收入等
     *
     * @param detailDTO
     * @param dynamicFeeItems
     */
    void fillSpecialSalaryParamForResigned(HrBillDetailDTO detailDTO, List<HrBillDetailItemsDTO> dynamicFeeItems);

    /**
     * 填充薪酬账单 应发工资,税前应发,个税,实发工资,费用合计,免税收入等
     *
     * @param detailDTO
     * @param dynamicFeeItemsDTOS
     */
    void fillSalaryParam(HrBillDetailDTO detailDTO, List<HrBillDetailItemsDTO> dynamicFeeItemsDTOS, Integer personalTaxStart, BigDecimal sumCurrentIncome, List<HrSpecialDeduction> specialDeductionList, List<HrQuickDeduction> hrQuickDeductions);

    /**
     * 计算其他账单费用合计
     *
     * @param detailDTO
     * @param dynamicFeeItemsDTOS
     */
    List<HrBillDetailItems> fillOtherBillParam(HrBillDetailDTO detailDTO, List<HrBillDetailItemsDTO> dynamicFeeItemsDTOS, Boolean flag);

    /**
     * 验证客户协议是否正常 并返回正常协议
     *
     * @param clientId
     * @return
     */
    HrProtocol checkProtocol(String clientId);



    /**
     * 薪酬账单-重新计算
     *
     * @param hrBillDTO
     * @return
     */
    List<HrBillDetailDTO> recalculationHrBillDetail(HrBillDTO hrBillDTO);

    /**
     * 社会治理账单用途切换
     * @param hrBillDTO
     * @return
     */
    List<HrBillDetailDTO> handleSocialGovernanceDetail(HrBillDTO hrBillDTO);

    /**
     * 处理薪酬账单绑定社保金额
     */
    void salaryBillAmount();

    /**
     *制动动态表头
     *
     * @param clientIds
     * @return
     */
    List<DynamicHeadersDTO> dynamicHeader(List<String> clientIds);

    /**
     * 保险基数重新赋值
     * @param socialSecurityDTO
     * @param billDetail
     */
    void fillDynamicCardinal(HrSocialSecurityDTO socialSecurityDTO, HrBillDetailDTO billDetail);

    /**
     * 获取客户配置的社保类型
     *
     * @param clientIds
     * @return
     */
    HrSocialSecurityDTO getClientSocialSecurity(List<String> clientIds);

    /**
     * 根据账单编号billNo查询账单，返回树形结构
     *
     * @param billNo
     * @param clientId
     * @return
     */
    List<HrBillDTO> findByBillNo(String billNo, String clientId);

    /**
     * 批量拷贝账单
     * @param hrBillDTO
     */
    HrBillDTO copyHrBillBatch(HrBillDTO hrBillDTO);

    /**
     * 根据账单查看动态表头
     * @param billIds
     * @return
     */
    List<DynamicHeadersDTO> showDynamicHeader(List<String> billIds);

    /**
     * 特殊客户填充薪酬账单
     */
    boolean handleFillSalaryParam(SpecialBillClient specialBillClient, HrBillDetailDTO detailDTO, List<HrBillDetailItemsDTO> dynamicFeeItems,
                                  Map<String, BillExcelDataDTO> specialBillExcelDataMap, HrBillDTO hrBillDTO, Integer lastPayYear, Integer lastPayMonthly,
                                  Integer personalTaxStart, BigDecimal sumCurrentIncome, List<HrSpecialDeduction> specialDeductionList, List<HrQuickDeduction> hrQuickDeductions, List<HrBillDetailDTO> billDetailByBill, List<HrBillDetailDTO> lastBillDetailDTOList, List<HrBillDetailItemsDTO> initDynamicFee);

    /**
     * 将特殊处理的员工其中一项费用增项设置为工资
     * @param initDynamicFee
     * @param salary
     */
    void fillSpecialBillDetailItems(List<HrBillDetailItemsDTO> initDynamicFee, BigDecimal salary);

    /**
     * 填充薪酬账单社保公积金金额
     *
     * @param billDetail
     * @param detailDTO
     */
    void fillSecurityAccumulation(HrBillDetailDTO billDetail, HrBillDetailDTO detailDTO);

    /**
     * 东区公安退休人员
     */
    void fillSpecialSalaryParamForRetire(HrBillDetailDTO detailDTO, List<HrBillDetailDTO> lastBillDetailDTOList, List<HrBillDetailItemsDTO> initDynamicFee, Integer personalTaxStart, BigDecimal sumCurrentIncome, List<HrSpecialDeduction> specialDeductionList, List<HrQuickDeduction> hrQuickDeductions);

    /**
     * 东区公安
     * 上月离职员工
     * 薪酬账填充应发工资,税前应发,个税,实发工资,费用合计,免税收入等
     *  @param detailDTO
     * @param dynamicFeeItems
     */
    void fillEastSecuritySalaryParamForResigned(HrBillDetailDTO detailDTO, List<HrBillDetailItemsDTO> dynamicFeeItems);

    /**
     * 将东区公安离职的员工费用增项重新赋值
     * @param billDetail
     * @param specialBillExcelDataMap
     * @param hrBillDetailItemsList
     */
    void fillEastSecurityBillDetailItems(HrBillDetailDTO billDetail, Map<String, BillExcelDataDTO> specialBillExcelDataMap, List<HrBillDetailItemsDTO> hrBillDetailItemsList);

    /**
     * 创建中石化账单汇总
     * @param hrBillDTO
     */
    void createSinopecBillTotal(HrBillDTO hrBillDTO);

    /**
     * 重置员工社保医保公积金为0
     *
     * @param billDetail
     */
    void resetStaffBillDetail(HrBillDetailDTO billDetail);

    /**
     * 乘法计算翻倍
     *
     * @param billDetail
     * @param hrBillDTO
     * @param emolumentMonthNum
     */
    void calStaffBillDetail(HrBillDetailDTO billDetail, HrBillDTO hrBillDTO, int emolumentMonthNum);

}
