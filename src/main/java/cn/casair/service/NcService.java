package cn.casair.service;

import cn.casair.dto.nc.param.VoucherDetailDTO;
import cn.casair.dto.nc.param.VoucherHeadDTO;
import cn.casair.dto.nc.result.NcResultDTO;

import java.util.List;

/**
 * NC系统相关
 */
public interface NcService {


    /**
     * 查询会计科目及辅助
     *
     * @return
     */
    Object qryAccountAuxiliary(String code, String name);

    /**
     * 查询客户档案
     */
    Object qryCustomer(String code, String name);


    /**
     * 生成凭证
     */
    NcResultDTO createVoucher(VoucherHeadDTO voucherHead, List<VoucherDetailDTO> voucherDetailList);

}
