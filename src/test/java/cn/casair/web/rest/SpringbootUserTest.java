package cn.casair.web.rest;

import cn.casair.CasairwebbaseApp;
import cn.casair.common.enums.ContractEnum;
import cn.casair.common.enums.ServiceCenterEnum;
import cn.casair.common.utils.PdfUtils;
import cn.casair.domain.*;
import cn.casair.dto.HrBillDetailDTO;
import cn.casair.dto.HrBillTotalDTO;
import cn.casair.dto.HrFeeReviewDTO;
import cn.casair.dto.formdata.SignRegionDTO;
import cn.casair.mapper.HrBillDetailMapper;
import cn.casair.mapper.HrBillTotalMapper;
import cn.casair.mapper.HrFeeReviewMapper;
import cn.casair.repository.*;
import cn.casair.service.HrApplyOpLogsService;
import cn.casair.service.HrBillDetailService;
import cn.casair.service.HrContractService;
import cn.casair.service.component.ecloud.ECloudComponent;
import com.agile.ecloud.sdk.bean.ECloudDomain;
import com.agile.ecloud.sdk.http.EcloudClient;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/12/03 14:45
 */
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CasairwebbaseApp.class)
public class SpringbootUserTest {

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Test
    public void getEClouldUserInfo() throws Exception {
        log.info("密码：{}", passwordEncoder.encode("c123456"));
    }


}
